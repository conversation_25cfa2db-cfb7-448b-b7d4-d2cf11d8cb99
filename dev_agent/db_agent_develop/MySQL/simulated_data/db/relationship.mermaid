---
config:
  theme: default
---
erDiagram
    CUSTOMERS ||--o{ CUSTOMER_ORDERS : "places"
    PRODUCTS ||--o{ BILLS_OF_MATERIALS : "contains"
    PRODUCTS ||--o{ CUSTOMER_ORDER_DETAIL : "is_ordered"
    PRODUCTS ||--o{ WORK_ORDERS : "is_produced"
    MATERIALS ||--o{ BILLS_OF_MATERIALS : "is_component"
    MATERIALS ||--o{ WORK_ORDER_MATERIAL : "is_used"
    MATERIALS ||--o{ PURCHASE_REQUEST : "is_requested"
    MATERIALS ||--o{ PURCHASE_ORDER_DETAIL : "is_ordered"
    MATERIALS ||--o{ GOODS_RECEIPT_DETAIL : "is_received"
    SUPPLIERS ||--o{ MATERIALS : "supplies"
    SUPPLIERS ||--o{ PURCHASE_ORDERS : "receives"
    FACTORIES ||--o{ WORK_ORDERS : "operates"
    EMPLOYEES ||--o{ PURCHASE_REQUEST : "manages"
    EMPLOYEES ||--o{ PURCHASE_ORDERS : "manages"
    EMPLOYEES ||--o{ GOODS_RECEIPT : "manages"
    CUSTOMER_ORDERS ||--o{ CUSTOMER_ORDER_DETAIL : "has_details"
    CUSTOMER_ORDERS }o--o{ WORK_ORDERS : "can_generate"
    WORK_ORDERS ||--o{ WORK_ORDER_MATERIAL : "consumes"
    PURCHASE_REQUEST ||--o{ PURCHASE_ORDERS : "fulfills_request"
    PURCHASE_ORDERS ||--o{ PURCHASE_ORDER_DETAIL : "has_items"
    PURCHASE_ORDERS ||--o{ GOODS_RECEIPT : "expects_receipt"
    GOODS_RECEIPT ||--o{ GOODS_RECEIPT_DETAIL : "details_items"
    CUSTOMERS {
        INT customer_id "客戶ID"
        VARCHAR customer_name "客戶名稱"
        VARCHAR contact_person "聯絡人"
        VARCHAR phone_number "聯絡電話"
        VARCHAR email "電子郵件"
        VARCHAR address "地址"
        VARCHAR tax_id "統一編號/稅號"
        VARCHAR payment_terms "付款條件"
        TEXT notes "備註"
        DATETIME created_at "建立時間"
        DATETIME updated_at "最後更新時間"
    }
    PRODUCTS {
        INT product_id "產品ID"
        VARCHAR product_code "產品編號 (unique)"
        VARCHAR product_name "產品名稱"
        TEXT description "產品描述"
        DECIMAL unit_price "產品單價"
        INT lead_time_days "標準生產交期 (天)"
        VARCHAR design_file_path "設計檔案路徑"
        DATETIME created_at "建立時間"
        DATETIME updated_at "最後更新時間"
    }
    MATERIALS {
        INT material_id "物料ID"
        VARCHAR material_code "物料編號 (unique)"
        VARCHAR material_name "物料名稱"
        VARCHAR specification "規格"
        VARCHAR unit_of_measure "計量單位"
        VARCHAR category "物料類別"
        DECIMAL current_stock "目前庫存量"
        DECIMAL min_stock_level "最低庫存量警告"
        DECIMAL max_stock_level "最高庫存量"
        INT supplier_id "供應商識別碼"
        DECIMAL unit_cost "單價"
        VARCHAR location "存放位置"
        DATETIME created_at "建立時間"
        DATETIME updated_at "最後更新時間"
    }
    SUPPLIERS {
        INT supplier_id "供應商ID"
        VARCHAR supplier_name "供應商名稱"
        VARCHAR contact_person "聯絡人"
        VARCHAR phone_number "聯絡電話"
        VARCHAR email "電子郵件"
        VARCHAR address "地址"
        VARCHAR tax_id "統一編號/稅號"
        TEXT notes "備註"
        DATETIME created_at "建立時間"
        DATETIME updated_at "最後更新時間"
    }
    BILLS_OF_MATERIALS {
        INT bom_id "BOM ID"
        INT product_id "產品ID"
        INT material_id "物料ID"
        DECIMAL quantity "所需物料數量"
        VARCHAR notes "備註"
        DATETIME created_at "建立時間"
        DATETIME updated_at "最後更新時間"
    }
    CUSTOMER_ORDERS {
        INT order_id "訂單ID"
        VARCHAR order_number "訂單編號 (unique)"
        INT customer_id "客戶ID"
        DATE order_date "訂單日期"
        DATE required_delivery_date "要求交期"
        DECIMAL total_amount "訂單總金額"
        VARCHAR order_status "訂單狀態 (如：生產中、已完成)"
        VARCHAR shipping_address "出貨地址"
        TEXT notes "備註"
        DATETIME created_at "建立時間"
        DATETIME updated_at "最後更新時間"
    }
    CUSTOMER_ORDER_DETAIL {
        INT order_detail_id "訂單明細ID"
        INT order_id "訂單ID"
        INT product_id "產品ID"
        INT quantity "訂購數量"
        DECIMAL unit_price "當時訂購的單價"
        DECIMAL subtotal "小計 (數量 * 單價)"
        VARCHAR notes "備註"
    }
    WORK_ORDERS {
        INT work_order_id "工單ID"
        VARCHAR work_order_number "工單編號 (unique)"
        INT order_id "相關客戶訂單ID"
        INT product_id "生產產品ID"
        INT quantity "計劃生產數量"
        DATE start_date "計劃開始日期"
        DATE end_date "計劃結束日期"
        DATETIME actual_start_date "實際開始日期時間"
        DATETIME actual_end_date "實際結束日期時間"
        INT priority "優先度"
        INT factory_id "生產工廠識別碼"
        TEXT notes "備註"
        DATETIME created_at "建立時間"
        DATETIME updated_at "最後更新時間"
    }
    WORK_ORDER_MATERIAL {
        INT wo_material_id "工單物料ID"
        INT work_order_id "工單ID"
        INT material_id "物料ID"
        DECIMAL required_quantity "計劃所需數量 (根據BOM計算)"
        DECIMAL issued_quantity "已領用數量"
        DATETIME issue_date "領用日期時間"
        VARCHAR notes "備註"
    }
    FACTORIES {
        INT factory_id "工廠ID"
        VARCHAR factory_name "工廠名稱"
        VARCHAR location "工廠地址"
        VARCHAR contact_person "聯絡人"
        VARCHAR phone_number "聯絡電話"
        TEXT notes "備註"
        DATETIME created_at "建立時間"
        DATETIME updated_at "最後更新時間"
    }
    PURCHASE_REQUEST {
        INT request_id "採購申請ID"
        VARCHAR request_number "申請單號 (unique)"
        DATE request_date "申請日期"
        VARCHAR department "申請部門"
        INT request_user_id "申請人識別碼"
        INT material_id "物料識別碼"
        DECIMAL quantity "申請數量"
        VARCHAR unit_of_measure "計量單位"
        DATE required_date "需求日期"
        TEXT notes "備註"
        VARCHAR request_status "申請狀態 (如：待審核、已核准)"
        INT approval_user_id "審核人識別碼"
        DATETIME approval_date "審核日期時間"
        DATETIME created_at "建立時間"
        DATETIME updated_at "最後更新時間"
    }
    PURCHASE_ORDERS {
        INT order_id "採購單ID"
        VARCHAR order_number "採購單號 (unique)"
        INT request_id "採購申請識別碼"
        INT supplier_id "供應商識別碼"
        DATE order_date "下單日期"
        DATE expected_delivery_date "預計交貨日期"
        DECIMAL total_amount "訂單總金額"
        VARCHAR order_status "訂單狀態 (如：已確認、已收貨)"
        VARCHAR payment_terms "付款條件"
        VARCHAR shipping_address "收貨地址"
        TEXT notes "備註"
        DATETIME created_at "建立時間"
        DATETIME updated_at "最後更新時間"
    }
    PURCHASE_ORDER_DETAIL {
        INT order_detail_id "採購單明細ID"
        INT order_id "採購單識別碼"
        INT material_id "物料識別碼"
        DECIMAL quantity "採購數量"
        DECIMAL unit_price "單價"
        DECIMAL subtotal "小計 (數量 * 單價)"
        VARCHAR notes "備註"
    }
    GOODS_RECEIPT {
        INT receipt_id "收貨記錄ID"
        INT order_id "採購單識別碼"
        DATETIME receipt_date "收貨日期時間"
        INT received_by_user_id "收貨人識別碼"
        TEXT notes "備註"
        DATETIME created_at "建立時間"
        DATETIME updated_at "最後更新時間"
    }
    GOODS_RECEIPT_DETAIL {
        INT receipt_detail_id "收貨明細ID"
        INT receipt_id "收貨記錄識別碼"
        INT material_id "物料識別碼"
        DECIMAL received_quantity "實收數量"
        VARCHAR unit_of_measure "計量單位"
        VARCHAR notes "備註"
    }
    EMPLOYEES {
        INT employee_id "員工ID"
        VARCHAR employee_number "員工編號 (unique)"
        VARCHAR full_name "姓名"
        VARCHAR job_title "職稱"
        VARCHAR department "部門"
        VARCHAR phone_number "聯絡電話"
        VARCHAR email "電子郵件"
        DATE hire_date "入職日期"
        TEXT notes "備註"
        DATETIME created_at "建立時間"
        DATETIME updated_at "最後更新時間"
    }