# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

`mcp-agent` is a framework for building effective AI agents using the Model Context Protocol (MCP). It implements the agent patterns described in Anthropic's "Building Effective Agents" paper with a focus on composability and MCP server integration.

## Common Development Commands

**Setup and Installation:**
- `uv sync --dev` - Install all dependencies including dev dependencies
- `uv add "dependency-name"` - Add new dependencies

**Code Quality:**
- `uv run scripts/lint.py --fix` - Lint and automatically fix code issues
- `uv run scripts/format.py` - Format code
- `pytest` - Run tests

**Development Tools:**
- `uv run scripts/example.py <path_to_example>` - Run examples in debug mode
- `uv run scripts/gen_schema.py` - Generate JSON schemas for configuration
- `uv run scripts/promptify.py` - Bundle repository for LLM prompts

**Running Examples:**
```bash
cd examples/basic/mcp_basic_agent
cp mcp_agent.secrets.yaml.example mcp_agent.secrets.yaml  # Update with your API keys
uv run main.py
```

## Core Architecture

### Main Components
- **MCPApp** (`src/mcp_agent/app.py`): Global state and configuration management
- **Agent** (`src/mcp_agent/agents/agent.py`): Entity with MCP server access and LLM integration
- **AugmentedLLM** (`src/mcp_agent/workflows/llm/`): LLM enhanced with MCP server tools
- **Workflows** (`src/mcp_agent/workflows/`): Implementation of agent patterns from Anthropic's paper

### Key Patterns Implemented
All workflow patterns are `AugmentedLLM` instances, enabling composition:
- **Parallel**: Fan-out/fan-in pattern for concurrent sub-agent execution
- **Router**: Route requests to appropriate agents/servers based on classification
- **Orchestrator**: High-level planning and delegation with dependency management
- **Evaluator-Optimizer**: Iterative improvement loop until quality criteria met
- **Swarm**: OpenAI Swarm pattern implementation (model-agnostic)
- **Intent Classifier**: Intent classification using embeddings or LLMs

### LLM Provider Support
Current branch `feature/vllm-support` adds VLLM support via OpenAI-compatible API:
- **VLLM**: Latest addition with "Thinking Mode" for Qwen models
- **OpenAI, Anthropic, Azure, Bedrock, Google, Ollama**: Existing providers
- All providers implement the same `AugmentedLLM` interface for consistency

## Configuration Management

**Required Files:**
- `mcp_agent.config.yaml` - Main configuration (committed to repo)
- `mcp_agent.secrets.yaml` - API keys and secrets (gitignored)

**Key Configuration Sections:**
```yaml
execution_engine: asyncio|temporal  # Temporal for durable execution
logger:
  transports: [console, file]
  level: debug
mcp:
  servers:
    server_name:
      command: "command"
      args: ["arg1", "arg2"]
```

## MCP Server Integration

MCP servers are defined in configuration and managed automatically:
- **Lifecycle Management**: Automatic connection/disconnection via context managers
- **Tool Discovery**: Tools from connected servers are automatically available to LLMs
- **Aggregation**: Multiple servers can be combined via `MCPAggregator`
- **Persistent Connections**: Use `MCPConnectionManager` for long-running workflows

## Development Patterns

**Creating New Agents:**
1. Look at existing examples in `examples/` directory
2. Follow naming conventions and patterns from similar agents
3. Use existing MCP servers when possible rather than creating new ones

**Adding New Workflow Patterns:**
1. Extend `AugmentedLLM` base class
2. Implement required methods: `generate`, `generate_str`, `generate_structured`
3. Ensure composability with other patterns

**Testing:**
- Limited test coverage currently exists
- Tests are in `tests/` directory using pytest with asyncio support
- Run examples to verify functionality

## Temporal Workflow Support

Optional integration with Temporal for durable execution:
- Install with `uv add "mcp-agent[temporal]"`
- Configure `execution_engine: temporal` in config
- Enables pause/resume and state serialization for long-running workflows

## Human Input Integration

Workflows can pause for human input:
- Use `human_input_callback` parameter in agents
- LLMs can call `__human_input__` tool to request user input
- Supports console input and custom callback implementations

## Important Notes

- **Model Agnostic**: All patterns work across different LLM providers
- **Composability**: Every workflow is an `AugmentedLLM`, enabling complex compositions
- **MCP-First**: Built specifically for MCP server ecosystem integration
- **Production Ready**: Includes logging, tracing, error handling, and durable execution
- **Extensive Examples**: 50+ examples covering various use cases and patterns

When making changes, ensure they maintain the composable nature of the framework and follow the existing patterns for consistency.

## Context Sharing Mechanisms in Multi-Agent Workflows

The mcp-agent framework implements sophisticated context sharing mechanisms that enable agents to collaborate effectively while maintaining proper isolation. This is best demonstrated in the Financial Analyzer example (`examples/usecases/mcp_financial_analyzer`).

### Context Data Structure and Storage

The context sharing system uses a **hierarchical state management approach** centered around these key data structures:

#### Core Data Models (`src/mcp_agent/workflows/orchestrator/orchestrator_models.py`)
- **`PlanResult`**: The primary context container storing the overall workflow state
- **`StepResult`**: Contains results from each sequential step in the workflow
- **`TaskWithResult`**: Stores individual agent task results with their outputs

```python
class PlanResult(BaseModel):
    objective: str                    # The main workflow goal
    plan: Plan | None = None         # The execution plan
    step_results: List[StepResult]   # Accumulated results from all steps
    is_complete: bool = False        # Whether workflow is finished
    result: str | None = None        # Final synthesized result
```

#### Context Storage Location
The context is maintained **in-memory** within the Orchestrator's execution loop at `src/mcp_agent/workflows/orchestrator/orchestrator.py:282`. Each workflow execution creates a new `PlanResult` instance that persists throughout the entire workflow lifecycle.

### Context Reading Mechanisms

#### Individual Agent Context Access
Agents access context through the **`TASK_PROMPT_TEMPLATE`** mechanism (`orchestrator_prompts.py:103`):

```python
TASK_PROMPT_TEMPLATE = """You are part of a larger workflow to achieve the objective: {objective}.
Your job is to accomplish only the following task: {task}.

Results so far that may provide helpful context:
{context}"""
```

#### Context Formatting for Agents
The `format_plan_result()` function (`orchestrator_models.py:138`) transforms the structured context into readable text:

```python
def format_plan_result(plan_result: PlanResult) -> str:
    steps_str = "\n\n".join(
        f"{i + 1}:\n{format_step_result(step)}"
        for i, step in enumerate(plan_result.step_results)
    )
    
    return PLAN_RESULT_TEMPLATE.format(
        plan_objective=plan_result.objective,
        steps_str=steps_str,
        plan_status="Complete" if plan_result.is_complete else "In Progress",
        plan_result=plan_result.result if plan_result.is_complete else "In Progress",
    )
```

### Context Writing and Updates

#### Result Collection Mechanism
Context updates occur in the `_execute_step()` method (`orchestrator.py:404`):

```python
async def _execute_step(self, step: Step, previous_result: PlanResult, ...):
    step_result = StepResult(step=step, task_results=[])
    
    # Execute subtasks in parallel
    for task, result in zip(step.tasks, results):
        step_result.add_task_result(
            TaskWithResult(**task.model_dump(), result=str(result))
        )
    
    # Format and store the step result
    step_result.result = format_step_result(step_result)
    return step_result
```

#### Context Persistence Pattern
Results are accumulated using the **additive pattern**:
```python
plan_result.add_step_result(step_result)  # Add each step's results to the main context
```

### Inter-Agent Context Propagation

#### Sequential Flow Pattern
Context flows between agents through the orchestrator's **sequential execution model**:

1. **Step 1**: Research agent gathers financial data
2. **Step 2**: Financial analyst receives Step 1 results via formatted context
3. **Step 3**: Report writer receives both Step 1 and Step 2 results

#### Context Injection Points
Context propagation occurs at two critical points:

**Planning Phase** (`orchestrator.py:441`):
```python
task_description = TASK_PROMPT_TEMPLATE.format(
    objective=previous_result.objective,
    task=task.description,
    context=context  # Formatted previous results
)
```

**Execution Phase** (`orchestrator.py:415`):
```python
context = format_plan_result(previous_result)  # Convert structured data to text
```

### Context Isolation and Scope

#### Workflow-Level Isolation
Each workflow execution maintains **complete isolation**:
- New `PlanResult` instance per workflow (`orchestrator.py:282`)
- No shared state between different workflow runs
- Context is scoped to the specific objective being pursued

#### Agent-Level Context Boundaries
Agents operate with **task-specific context scoping**:
- Each agent only receives relevant context through the formatted prompt
- Agents cannot directly access other agents' internal state
- Context is read-only for individual agents

#### EvaluatorOptimizerLLM Context Management
The nested evaluator-optimizer workflow demonstrates **hierarchical context isolation**:
```python
class EvaluatorOptimizerLLM:
    def __init__(self, optimizer: Agent, evaluator: Agent, ...):
        self.refinement_history = []  # Internal context state
```

### Key Implementation Insights

1. **Context is Text-Based**: All context sharing uses formatted strings rather than structured objects for LLM consumption
2. **Sequential Dependency**: Each step builds upon all previous steps' results
3. **Template-Driven**: Consistent formatting ensures reliable context interpretation
4. **Isolation by Design**: No cross-workflow or direct inter-agent state sharing
5. **Hierarchical Composition**: Complex workflows (like EvaluatorOptimizerLLM) maintain their own internal context while participating in the larger workflow context

This architecture enables multi-agent workflows to maintain coherent state across complex pipelines while ensuring proper isolation and scalability.

## SSE Transport Integration Achievement

**Date**: July 31, 2025  
**Integration**: SSE Transport for Storage Index Calculation

Successfully integrated Server-Sent Events (SSE) transport from `/merge/agent_develop/index/server.py` to support real-time storage index calculation in the financial analyzer (`examples/usecases/mcp_financial_analyzer`).

### Implementation Highlights

**Core Integration Components:**
- **MCP SSE Configuration**: Updated `mcp_agent.config.yaml` with proper SSE transport settings (`transport: sse`, `url: http://localhost:6970/sse`)
- **Enhanced Agent Architecture**: Replaced direct server imports with MCP-native SSE transport calls in `shortage_analyzer_agent.py`
- **Server Health Management**: Added comprehensive server availability validation and startup management in `main.py`
- **Deployment Utilities**: Enhanced `scripts/start_shortage_server.py` with SSE endpoint health checking

**Key Features Delivered:**
- ✅ **SSE Server Integration**: Real-time connection to shortage calculation tools via MCP protocol
- ✅ **Robust Fallback System**: Local calculations maintain functionality when MCP unavailable
- ✅ **Advanced Data Processing**: 8+ financial text parsing patterns for component extraction
- ✅ **Risk Analysis Engine**: Enhanced risk classification with business rules and critical component detection
- ✅ **Production-Ready Error Handling**: Multi-layer error recovery with graceful degradation

**Technical Achievements:**
- **ShortageIndex Tool**: Basic shortage calculation via SSE transport with fallback
- **WeightedShortageIndex Tool**: Strategic component analysis with weight normalization
- **Server Health Validation**: Real-time SSE endpoint monitoring and availability checks
- **Configuration Management**: Proper MCP server settings with transport-specific validation

**Usage Example:**
```bash
# Server management
python scripts/start_shortage_server.py status
python scripts/start_shortage_server.py sse-check

# Run shortage analysis with SSE transport
python main.py
```

**Results:**
- **Basic Shortage Index**: 0.833 (HIGH risk) for test scenario
- **Weighted Shortage Index**: 0.626 (HIGH risk) with strategic weighting
- **Component Analysis**: Detailed analysis of CPU, GPU, motherboard, and fans
- **Risk Recommendations**: 8-9 actionable recommendations per analysis

This integration demonstrates the framework's capability to seamlessly incorporate external MCP servers via SSE transport while maintaining robust fallback mechanisms for production reliability.