# MCP Financial Analyzer with Google Search and Supply Chain Intelligence

This example demonstrates a comprehensive financial analysis Agent application that uses an orchestrator with smart data verification to coordinate specialized agents for generating financial reports with integrated shortage analysis and intelligent alerting capabilities.

https://github.com/user-attachments/assets/d6049e1b-1afc-4f5d-bebf-ed9aece9acfc

## How It Works

1. **Orchestrator**: Coordinates the entire workflow, managing the flow of data between agents and ensuring each step completes successfully
2. **Research Agent & Research Evaluator**: Work together in a feedback loop where the Research Agent collects data and the Research Evaluator assesses its quality
3. **EvaluatorOptimizer** (Research Quality Controller): Manages the feedback loop, evaluating outputs and directing the Research Agent to improve data until reaching EXCELLENT quality rating
4. **Analyst Agent**: Analyzes the verified data to identify key financial insights
5. **Enhanced Shortage Analyzer Agent**: Provides comprehensive supply chain risk assessment with advanced shortage index calculations, weighted analysis, and intelligent risk classification using the agent_develop shortage-index MCP service
6. **Alert Manager Agent**: Evaluates thresholds and sends intelligent notifications across multiple channels
7. **Report Writer**: Creates a professional markdown report saved to the filesystem including all analyses

This approach ensures high-quality reports by focusing on data verification before proceeding with analysis, enhanced with real-time supply chain intelligence and proactive alerting capabilities.

```plaintext
┌──────────────┐      ┌──────────────────┐      ┌────────────────────┐
│ Orchestrator │─────▶│ Research Quality │─────▶│      Research      │◀─┐
│   Workflow   │      │    Controller    │      │        Agent       │  │
└──────────────┘      └──────────────────┘      └────────────────────┘  │
       │                                                   │            │
       │                                                   │            │
       │                                                   ▼            │
       │                                        ┌────────────────────┐  │
       │                                        │ Research Evaluator ├──┘
       │                                        │        Agent       │
       │                                        └────────────────────┘
       │             ┌─────────────────┐
       ├────────────▶│  Analyst Agent  │
       │             └─────────────────┘
       │             ┌─────────────────┐       ┌────────────────────┐
       ├────────────▶│ Shortage Analyzer│──────▶│   agent_develop    │
       │             │      Agent      │       │  index service     │
       │             └─────────────────┘       └────────────────────┘
       │             ┌─────────────────┐       ┌────────────────────┐
       ├────────────▶│ Alert Manager   │──────▶│   agent_develop    │
       │             │      Agent      │       │notification service│
       │             └─────────────────┘       └────────────────────┘
       │             ┌─────────────────┐
       └────────────▶│  Report Writer  │
                     │      Agent      │
                     └─────────────────┘
```

## `1` App set up

First, clone the repo and navigate to the financial analyzer example:

```bash
git clone https://github.com/lastmile-ai/mcp-agent.git
cd mcp-agent/examples/usecases/mcp_financial_analyzer
```

Install `uv` (if you don’t have it):

```bash
pip install uv
```

Sync `mcp-agent` project dependencies:

```bash
uv sync
```

Install requirements specific to this example:

```bash
uv pip install -r requirements.txt
```

Install the g-search-mcp server (from https://github.com/jae-jae/g-search-mcp):

```bash
npm install -g g-search-mcp
```

### Optional: Set up agent_develop services for shortage analysis and alerting

For enhanced supply chain intelligence and alerting capabilities, you can optionally set up the agent_develop services:

1. Clone and set up agent_develop repository (if available)
2. **Automatic Startup**: The application will automatically start the shortage index server when needed
   - No manual intervention required for shortage analysis functionality
   - Server starts automatically on port 6970 when shortage analysis is enabled

3. For manual control, use the provided service management:
   ```bash
   # Check if shortage server is running
   python scripts/start_shortage_server.py status
   
   # Manually start the shortage server
   python scripts/start_shortage_server.py start
   
   # Stop the shortage server
   python scripts/start_shortage_server.py stop
   ```

**Note**: The application includes intelligent server management and will gracefully degrade if services cannot be started, continuing with core financial analysis functionality.

## `2` Set up secrets and environment variables

Copy and configure your secrets:

```bash
cp mcp_agent.secrets.yaml.example mcp_agent.secrets.yaml
```

Then open `mcp_agent.secrets.yaml` and add your API key for your preferred LLM (OpenAI):

```yaml
openai:
  api_key: "YOUR_OPENAI_API_KEY"
```

### Optional: Configure integration services

To enable shortage analysis and alerting features, you can configure service endpoints via environment variables:

```bash
export SHORTAGE_INDEX_URL="http://localhost:6970"
export ALERT_NOTIFICATION_URL="http://localhost:6971"
export ENABLE_SHORTAGE_ANALYSIS="true"
export ENABLE_ALERT_MANAGEMENT="true"
```

**Note**: The shortage index server now runs on port 6970 by default and will be automatically started when needed.

Or modify the configuration directly in `config/integration_config.py`.

## `3` Run locally

Run your MCP Agent app with a company name:

```bash
uv run main.py "Apple"
```

Or run with a different company:

```bash
uv run main.py "Microsoft"
```

## Enhanced Features

### Supply Chain Shortage Analysis

The financial analyzer now includes advanced supply chain intelligence capabilities:

#### Features
- **Shortage Index Calculation**: Real-time shortage indices using external MCP services
- **Risk Level Classification**: Automatic classification into LOW/MEDIUM/HIGH risk categories
- **Supply Chain Vulnerability Assessment**: Comprehensive analysis of supply chain risks
- **Integration with Financial Data**: Shortage metrics incorporated into financial analysis pipeline

#### Configuration
The shortage analyzer can be configured with custom thresholds:
- **LOW risk**: Shortage index < 30
- **MEDIUM risk**: Shortage index 30-70  
- **HIGH risk**: Shortage index > 70

These thresholds can be adjusted in `config/integration_config.py`.

### Intelligent Alert Management

Multi-channel notification system for proactive monitoring:

#### Supported Channels
- **Email**: SMTP-based email notifications
- **MQTT**: Real-time messaging for IoT and event-driven systems
- **HTTP Webhooks**: REST API integration with external systems

#### Alert Features
- **Threshold-based Evaluation**: Intelligent alerting based on configurable thresholds
- **Multi-severity Levels**: Critical, Warning, and Info alert levels
- **Rate Limiting**: Spam prevention and alert frequency control
- **Graceful Degradation**: Continues operation even if notification services are unavailable

#### Configuration
Alert settings can be customized in `config/integration_config.py`:

```python
ALERT_THRESHOLDS = {
    "shortage_index": {
        "critical": 80,
        "warning": 50
    },
    "financial_metrics": {
        "revenue_change": -0.15,
        "profit_margin": 0.05
    }
}
```

## Service Management

### Starting Services

Use the provided service management script:

```bash
# Start all agent_develop services
python scripts/start_services.py --start

# Stop all services
python scripts/start_services.py --stop

# Check service health
python scripts/start_services.py --status
```

### Health Monitoring

The application includes built-in health monitoring:
- Automatic service discovery
- Health checks with retry logic
- Graceful degradation when services are unavailable
- Process monitoring and restart capabilities

## Testing

Run the comprehensive test suite:

```bash
# Run all tests
pytest

# Run specific test categories
pytest tests/test_shortage_analyzer.py           # Unit tests
pytest tests/test_enhanced_shortage_integration.py  # Integration tests
pytest tests/test_shortage_e2e_workflow.py       # End-to-end tests
pytest tests/test_alert_integration.py           # Alert management tests
```

### Test Coverage
- **Shortage Analysis**: Agent creation, calculation logic, risk classification
- **Alert Management**: Multi-channel notifications, threshold evaluation, delivery handling
- **Integration**: End-to-end workflow testing with mock services
- **Performance**: Benchmarks and scalability tests

## Troubleshooting

### Common Issues

#### Services Not Available
If agent_develop services are not running:
- Check service status: `python scripts/start_services.py --status`
- Verify port availability: `netstat -an | grep 6970`
- Review service logs for errors

#### Network Connectivity
If services are running but not accessible:
- Verify firewall settings allow connections to ports 6970-6971
- Check service endpoints in configuration
- Test connectivity: `curl http://localhost:6970/health`

#### Configuration Issues
- Ensure all required environment variables are set
- Verify API keys in `mcp_agent.secrets.yaml`
- Check service URLs in `config/integration_config.py`

### Performance Optimization

For better performance with large datasets:
- Increase timeout values in service configuration
- Enable connection pooling for MCP servers
- Consider using async/await patterns for concurrent operations

### Debugging

Enable debug logging by setting:
```bash
export LOG_LEVEL=debug
```

This will provide detailed information about:
- Service connection attempts
- MCP server communication
- Agent execution flow
- Error details and stack traces

## Architecture Details

### Integration Pattern
The system uses an **external MCP server pattern** where agent_develop services are added as external MCP servers that the financial analyzer can call through its existing agent framework.

### Data Flow
1. **Research Phase**: Research Agent gathers financial data using Google Search MCP
2. **Quality Control**: Research Evaluator ensures data quality through feedback loops
3. **Financial Analysis**: Analyst Agent performs core financial analysis
4. **Supply Chain Analysis**: Shortage Analyzer calculates shortage indices and risk levels
5. **Alert Evaluation**: Alert Manager evaluates thresholds and triggers notifications
6. **Report Generation**: Report Writer creates comprehensive reports including all analyses

### Service Dependencies
- **Core Services**: Always available (Research, Analyst, Report Writer)
- **Enhanced Services**: Optional (Shortage Analyzer, Alert Manager)
- **External Dependencies**: agent_develop services (graceful degradation if unavailable)

This architecture ensures backward compatibility while providing enhanced capabilities when additional services are available.

## Enhanced Shortage Analyzer Agent

The Enhanced Shortage Analyzer Agent is a comprehensive supply chain risk assessment tool that provides advanced shortage analysis capabilities:

### Key Features

- **Dual MCP Tool Integration**: Uses both ShortageIndexTool and WeightedShortageIndexTool from agent_develop
- **Advanced Data Processing**: Supports multiple input formats (text, structured data, quantity lists)
- **Intelligent Risk Classification**: Three-tier risk system (LOW/MEDIUM/HIGH) with business rules
- **Comprehensive Error Handling**: Graceful degradation with fallback calculations
- **Industry-Specific Analysis**: Customized processing for technology and manufacturing sectors
- **Performance Optimized**: Handles concurrent analyses and large component datasets

### Usage Examples

#### Basic Analysis
```python
from agents.shortage_analyzer_agent import create_shortage_analyzer_agent
from schemas.agent_schemas import ShortageAnalysisInputSchema

agent = create_shortage_analyzer_agent("Your Company")

input_data = ShortageAnalysisInputSchema(
    company_name="TechCorp",
    financial_data="CPU available 150, required 200, weight 0.3",
    message="Analyze Q4 supply chain risks"
)

result = await agent.enhanced_shortage_analysis(input_data)
print(f"Risk Level: {result.risk_level}")
print(f"Shortage Index: {result.shortage_index:.3f}")
```

#### Structured Component Analysis
```python
input_data = ShortageAnalysisInputSchema(
    company_name="Manufacturing Corp",
    components={
        "raw_materials": {"required": 1000, "available": 750},
        "finished_goods": {"required": 300, "available": 280}
    },
    weights={"raw_materials": 0.7, "finished_goods": 0.3}
)

result = await agent.enhanced_shortage_analysis(input_data)
```

### Testing

Run shortage analyzer specific tests:

```bash
# Unit tests
pytest tests/test_shortage_analyzer.py -v

# Integration tests with real MCP services
pytest tests/test_enhanced_shortage_integration.py -m real_service -v

# End-to-end workflow tests
pytest tests/test_shortage_e2e_workflow.py -m e2e -v

# Performance validation
pytest tests/test_shortage_performance_validation.py -m performance -v
```

### Documentation

Comprehensive documentation is available:
- **Implementation Guide**: `docs/shortage_analyzer_implementation_guide.md`
- **Architecture Design**: `design/shortage_analyzer_architecture.md`
- **API Reference**: Included in implementation guide
- **Test Examples**: See test files for detailed usage patterns

### Service Requirements

The Enhanced Shortage Analyzer requires:
- **agent_develop shortage-index service** running on port 6970 (automatically managed)
- **Python 3.8+** with async/await support
- **Memory**: Recommended 512MB+ for large datasets
- **Network**: Access to MCP server endpoints

### Automatic Service Management

The financial analyzer includes intelligent service management:
- **Auto-detection**: Automatically detects if shortage index server is running
- **Auto-startup**: Launches the server automatically when shortage analysis is enabled
- **Port Configuration**: Uses port 6970 by default (configurable)
- **Error Handling**: Graceful degradation if server cannot be started
- **Health Monitoring**: Continuous monitoring of server availability

For detailed setup and configuration instructions, see the implementation guide.
