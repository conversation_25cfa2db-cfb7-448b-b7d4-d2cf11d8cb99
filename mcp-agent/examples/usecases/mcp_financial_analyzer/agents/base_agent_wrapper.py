"""
BaseAgent wrapper implementation that maintains Agent interface compatibility.
Provides enhanced capabilities through atomic-agents while preserving existing workflow integration.
"""

import os
import asyncio
from typing import List, Optional, Callable, Dict, Any
from mcp_agent.agents.agent import Agent
from mcp_agent.workflows.llm.augmented_llm_vllm import VLLMAugmented<PERSON><PERSON>
from mcp_agent.workflows.llm.augmented_llm_openai import OpenAIAugmentedLLM
from schemas.agent_schemas import InstructionInputSchema, InstructionOutputSchema

try:
    from atomic_agents.agents.base_agent import BaseAgent, BaseAgentConfig
    from atomic_agents.lib.components.agent_memory import AgentMemory
    from atomic_agents.lib.components.system_prompt_generator import SystemPromptGenerator
    import instructor
    from openai import OpenAI
    ATOMIC_AGENTS_AVAILABLE = True
except ImportError:
    # Fallback when atomic-agents is not available
    ATOMIC_AGENTS_AVAILABLE = False
    BaseAgent = None
    BaseAgentConfig = None
    AgentMemory = None
    SystemPromptGenerator = None
    instructor = None
    OpenAI = None


class BaseAgentWrapper(Agent):
    """
    Wrapper that uses BaseAgent internally while maintaining Agent interface.
    
    This allows seamless integration with existing orchestrator and evaluator components
    while adding atomic-agents benefits like memory management, schema validation,
    and structured prompt generation.
    """
    
    def __init__(
        self, 
        name: str, 
        instruction: str, 
        server_names: List[str], 
        model: str = "Qwen/Qwen3-32B",
        **kwargs
    ):
        """
        Initialize BaseAgentWrapper with enhanced capabilities.
        
        Args:
            name: Agent name
            instruction: Agent instruction/prompt
            server_names: List of MCP server names
            model: Model to use for LLM (default: Qwen/Qwen3-32B)
            **kwargs: Additional Agent parameters
        """
        # Initialize parent Agent class to maintain interface compatibility
        super().__init__(
            name=name,
            instruction=instruction, 
            server_names=server_names,
            **kwargs
        )
        
        # Store model for LLM initialization
        self._model = model
        self._llm_initialized = False
        
        # Initialize BaseAgent if available
        self._base_agent = None
        self._enhanced_mode = True
        
        if ATOMIC_AGENTS_AVAILABLE:
            self._initialize_base_agent(instruction)
        else:
            print(f"Warning: atomic-agents not available, {name} running in compatibility mode")
            self._enhanced_mode = False
    
    def _initialize_base_agent(self, instruction: str):
        """Initialize the internal BaseAgent with proper configuration"""
        try:
            # Configure OpenAI client for BaseAgent
            api_key = os.getenv("OPENAI_API_KEY")
            if not api_key:
                print("Warning: OPENAI_API_KEY not found, using placeholder")
                api_key = "dummy-key-for-testing"
            
            client = instructor.from_openai(OpenAI(api_key=api_key))
            
            # Create enhanced memory with financial context
            memory = AgentMemory(max_messages=100)
            
            # Create system prompt generator with instruction
            system_prompt = SystemPromptGenerator(
                background=[
                    "You are a professional financial analysis agent.",
                    instruction
                ],
                steps=[
                    "Carefully analyze the provided input",
                    "Execute required actions using available tools", 
                    "Provide clear, factual responses with proper citations"
                ]
            )
            
            # Configure BaseAgent
            config = BaseAgentConfig(
                client=client,
                model="Qwen/Qwen3-32B",
                input_schema=InstructionInputSchema,
                output_schema=InstructionOutputSchema,
                memory=memory,
                system_prompt_generator=system_prompt,
                model_api_parameters={"temperature": 0.1}  # Low temperature for factual responses
            )
            
            # Create internal BaseAgent
            self._base_agent = BaseAgent(config)
            
        except Exception as e:
            print(f"Warning: Failed to initialize BaseAgent for {self.name}: {e}")
            self._base_agent = None
            self._enhanced_mode = False
    
    def enable_enhanced_processing(self, enabled: bool = True):
        """
        Toggle enhanced BaseAgent processing.
        
        Args:
            enabled: Whether to enable enhanced processing
        """
        self._enhanced_mode = enabled and self._base_agent is not None
    
    async def process_with_base_agent(
        self, 
        message: str, 
        context: Optional[str] = None
    ) -> str:
        """
        Process message using BaseAgent for enhanced capabilities.
        
        This method can be called by orchestrator for improved processing
        while maintaining fallback compatibility.
        
        Args:
            message: Input message to process
            context: Optional context information
            
        Returns:
            Processed response string
        """
        if not self._enhanced_mode or not self._base_agent:
            return message
            
        try:
            input_data = InstructionInputSchema(
                message=message,
                context=context
            )
            result = self._base_agent.run(input_data)
            return result.response
        except Exception as e:
            # Fallback to original behavior on error
            print(f"BaseAgent processing failed for {self.name}: {e}, falling back to original")
            return message
    
    def get_conversation_history(self) -> List[Dict[str, Any]]:
        """
        Get conversation history from BaseAgent memory.
        
        Returns:
            List of conversation messages
        """
        if self._base_agent and hasattr(self._base_agent, 'memory'):
            try:
                return self._base_agent.memory.get_history()
            except Exception as e:
                print(f"Failed to get conversation history for {self.name}: {e}")
        return []
    
    def clear_memory(self):
        """Clear BaseAgent conversation memory"""
        if self._base_agent and hasattr(self._base_agent, 'memory'):
            try:
                self._base_agent.memory.clear()
            except Exception as e:
                print(f"Failed to clear memory for {self.name}: {e}")
    
    def get_memory_summary(self) -> Dict[str, Any]:
        """
        Get summary of memory state.
        
        Returns:
            Dictionary with memory statistics
        """
        if not self._base_agent or not hasattr(self._base_agent, 'memory'):
            return {"available": False, "message_count": 0}
        
        try:
            history = self._base_agent.memory.get_history()
            return {
                "available": True,
                "message_count": len(history),
                "enhanced_mode": self._enhanced_mode
            }
        except Exception:
            return {"available": False, "message_count": 0}
    
    def is_enhanced_mode_available(self) -> bool:
        """
        Check if enhanced mode is available and working.
        
        Returns:
            True if BaseAgent is available and functional
        """
        return self._enhanced_mode and self._base_agent is not None
    
    def get_enhancement_status(self) -> Dict[str, Any]:
        """
        Get detailed status of enhancement capabilities.
        
        Returns:
            Dictionary with enhancement status information
        """
        return {
            "atomic_agents_available": ATOMIC_AGENTS_AVAILABLE,
            "base_agent_initialized": self._base_agent is not None,
            "enhanced_mode_enabled": self._enhanced_mode,
            "memory_available": (
                self._base_agent is not None and 
                hasattr(self._base_agent, 'memory')
            ),
            "agent_name": self.name,
            "server_names": self.server_names,
            "llm_initialized": self._llm_initialized,
            "llm_available": self.llm is not None
        }

    async def ensure_llm_initialized(self):
        """Ensure the LLM is properly initialized for MCP operations."""
        if self._llm_initialized and self.llm is not None:
            return
        
        try:
            # Determine which LLM to use based on model and configuration
            if self._model.startswith("Qwen") or "vllm" in self._model.lower():
                # Use VLLM for Qwen models with the specified model
                await self.attach_llm(lambda agent: VLLMAugmentedLLM(agent=agent, default_model=self._model))
            else:
                # Use OpenAI for other models with the specified model
                await self.attach_llm(lambda agent: OpenAIAugmentedLLM(agent=agent, default_model=self._model))
            
            self._llm_initialized = True
            print(f"✓ LLM initialized for agent {self.name} with model {self._model}")
            
        except Exception as e:
            print(f"✗ Failed to initialize LLM for agent {self.name}: {e}")
            raise


# Utility function for creating enhanced agents
def create_enhanced_agent(
    name: str,
    instruction: str, 
    server_names: List[str],
    **kwargs
) -> BaseAgentWrapper:
    """
    Factory function to create enhanced agents with BaseAgent capabilities.
    
    Args:
        name: Agent name
        instruction: Agent instruction
        server_names: MCP server names
        **kwargs: Additional Agent parameters
        
    Returns:
        BaseAgentWrapper instance
    """
    return BaseAgentWrapper(
        name=name,
        instruction=instruction,
        server_names=server_names,
        **kwargs
    )
