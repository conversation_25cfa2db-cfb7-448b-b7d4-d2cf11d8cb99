"""
Enhanced Shortage Analyzer Agent for financial analysis integration with agent_develop services.
Provides comprehensive supply chain risk assessment and shortage index calculation capabilities.
"""

import logging
import re
import json
from typing import Dict, List, Optional, Any
from datetime import datetime

from agents.base_agent_wrapper import create_enhanced_agent, BaseAgentWrapper
from schemas.agent_schemas import ShortageAnalysisInputSchema, ShortageAnalysisOutputSchema
# MCP integration is now handled directly through the Agent class

# Configure logging
logger = logging.getLogger("shortage_analyzer")


class MCPToolIntegration:
    """MCP-based integration with shortage-index server via SSE transport"""

    def __init__(self, agent):
        """Initialize with MCP agent reference"""
        self.agent = agent
        self.timeout = 30
        self.max_retries = 3

    async def call_shortage_index_tool(
        self,
        required_qty: List[float],
        available_qty: List[float]
    ) -> Dict[str, Any]:
        """Call ShortageIndex tool via MCP SSE transport"""

        try:
            logger.info(f"Calling ShortageIndex via MCP with required_qty={required_qty}, available_qty={available_qty}")

            # Check if agent has proper MCP integration
            if not hasattr(self.agent, 'llm') or self.agent.llm is None:
                logger.warning("Agent LLM not available, cannot use MCP tools directly")
                raise RuntimeError("MCP integration not available - agent.llm is None")

            # Use the agent's MCP connection to call the tool
            result = await self.agent.llm.generate_str(
                f"""Use the ShortageIndex tool to calculate shortage index.
                
                Parameters:
                - required_qty: {required_qty}
                - available_qty: {available_qty}
                
                Call the tool and return only the numerical result."""
            )
            
            logger.info(f"MCP ShortageIndex result: {result}")
            
            # Parse the result - handle natural language responses
            try:
                import re
                # First try to extract decimal numbers (including ones with leading digits)
                decimal_numbers = re.findall(r'\d+\.\d+', result)
                if decimal_numbers:
                    shortage_index = float(decimal_numbers[-1])  # Use the last decimal number
                else:
                    # If no decimal numbers, try to find any numbers
                    numbers = re.findall(r'\d+', result)
                    if numbers:
                        shortage_index = float(numbers[-1])  # Use the last integer
                    else:
                        # If no numbers found, try to parse the whole string
                        shortage_index = float(result.strip())
                
                return {
                    "shortage_index": shortage_index,
                    "error": None,
                    "tool_name": "ShortageIndex",
                    "calculation_method": "mcp_sse"
                }
                
            except (ValueError, IndexError) as parse_error:
                logger.warning(f"Failed to parse ShortageIndex result: {parse_error}")
                return {
                    "shortage_index": 0.5,  # Conservative fallback
                    "error": f"Result parsing error: {parse_error}",
                    "tool_name": "ShortageIndex",
                    "calculation_method": "mcp_sse_fallback"
                }

        except Exception as e:
            error_msg = f"MCP ShortageIndex call failed: {e}"
            logger.error(error_msg)
            return {
                "shortage_index": None,
                "error": error_msg,
                "tool_name": "ShortageIndex",
                "calculation_method": "failed"
            }

    async def call_weighted_shortage_tool(
        self,
        required_qty: List[float],
        available_qty: List[float],
        weights: List[float]
    ) -> Dict[str, Any]:
        """Call WeightedShortageIndex tool via MCP SSE transport"""

        try:
            logger.info(f"Calling WeightedShortageIndex via MCP with required_qty={required_qty}, available_qty={available_qty}, weights={weights}")

            # Check if agent has proper MCP integration
            if not hasattr(self.agent, 'llm') or self.agent.llm is None:
                logger.warning("Agent LLM not available, cannot use MCP tools directly")
                raise RuntimeError("MCP integration not available - agent.llm is None")

            # Use the agent's MCP connection to call the tool
            result = await self.agent.llm.generate_str(
                f"""Use the WeightedShortageIndex tool to calculate weighted shortage index.
                
                Parameters:
                - required_qty: {required_qty}
                - available_qty: {available_qty}
                - weight: {weights}
                
                Call the tool and return only the numerical result."""
            )
            
            logger.info(f"MCP WeightedShortageIndex result: {result}")
            
            # Parse the result - handle natural language responses
            try:
                import re
                # First try to extract decimal numbers (including ones with leading digits)
                decimal_numbers = re.findall(r'\d+\.\d+', result)
                if decimal_numbers:
                    weighted_shortage_index = float(decimal_numbers[-1])  # Use the last decimal number
                else:
                    # If no decimal numbers, try to find any numbers
                    numbers = re.findall(r'\d+', result)
                    if numbers:
                        weighted_shortage_index = float(numbers[-1])  # Use the last integer
                    else:
                        # If no numbers found, try to parse the whole string
                        weighted_shortage_index = float(result.strip())
                
                return {
                    "weighted_shortage_index": weighted_shortage_index,
                    "error": None,
                    "tool_name": "WeightedShortageIndex",
                    "calculation_method": "mcp_sse"
                }
                
            except (ValueError, IndexError) as parse_error:
                logger.warning(f"Failed to parse WeightedShortageIndex result: {parse_error}")
                return {
                    "weighted_shortage_index": 0.5,  # Conservative fallback
                    "error": f"Result parsing error: {parse_error}",
                    "tool_name": "WeightedShortageIndex",
                    "calculation_method": "mcp_sse_fallback"
                }

        except Exception as e:
            error_msg = f"MCP WeightedShortageIndex call failed: {e}"
            logger.error(error_msg)
            return {
                "weighted_shortage_index": None,
                "error": error_msg,
                "tool_name": "WeightedShortageIndex",
                "calculation_method": "failed"
            }

    async def execute_shortage_analysis(
        self,
        required_qty: List[float],
        available_qty: List[float],
        weights: Optional[List[float]] = None
    ) -> Dict[str, Any]:
        """Execute shortage analysis using MCP SSE transport - NO FALLBACKS"""

        results = {
            "service_available": True,
            "shortage_index": None,
            "weighted_shortage_index": None,
            "calculation_method": "mcp_sse",
            "errors": []
        }

        logger.info("✓ Executing shortage analysis via MCP SSE transport - NO FALLBACKS")
        
        # Call basic shortage index tool - REQUIRED
        shortage_result = await self.call_shortage_index_tool(required_qty, available_qty)

        if shortage_result["error"] is None:
            results["shortage_index"] = shortage_result["shortage_index"]
            logger.info("✓ Basic shortage index calculation completed via MCP SSE")
        else:
            results["errors"].append(f"ShortageIndex tool error: {shortage_result['error']}")
            logger.error(f"ShortageIndex FAILED - NO FALLBACK: {shortage_result['error']}")
            # NO FALLBACK - FAIL IMMEDIATELY
            results["service_available"] = False
            results["calculation_method"] = "failed"
            raise RuntimeError(f"MCP SSE ShortageIndex tool failed: {shortage_result['error']}")

        # Call weighted shortage tool if weights provided
        if weights:
            weighted_result = await self.call_weighted_shortage_tool(
                required_qty, available_qty, weights
            )

            if weighted_result["error"] is None:
                results["weighted_shortage_index"] = weighted_result["weighted_shortage_index"]
                logger.info("✓ Weighted shortage index calculation completed via MCP SSE")
            else:
                results["errors"].append(f"WeightedShortageIndex tool error: {weighted_result['error']}")
                logger.error(f"WeightedShortageIndex FAILED - NO FALLBACK: {weighted_result['error']}")
                # NO FALLBACK - FAIL IMMEDIATELY
                results["service_available"] = False
                results["calculation_method"] = "failed"
                raise RuntimeError(f"MCP SSE WeightedShortageIndex tool failed: {weighted_result['error']}")

        return results



class ShortageDataProcessor:
    """Process and extract shortage-related data from financial information"""

    def __init__(self):
        # Enhanced regex patterns for data extraction
        self.inventory_pattern = re.compile(
            r'(\w+)\s+available\s+(?:is\s+)?(\d+(?:\.\d+)?),?\s+(?:require|need)(?:d|s)?\s+(?:is\s+)?(\d+(?:\.\d+)?)',
            re.IGNORECASE
        )

        # Additional pattern for "Available X units, Required Y units" format
        self.inventory_pattern_alt = re.compile(
            r'Available\s+(\d+(?:\.\d+)?)\s+units?,?\s+Required\s+(\d+(?:\.\d+)?)\s+units?',
            re.IGNORECASE
        )

        # Pattern for component names with parentheses like "Primary Processors (CPU)"
        self.component_name_pattern = re.compile(
            r'([^:]+?)\s*\([^)]+\)\s*:\s*Available\s+(\d+(?:\.\d+)?)\s+units?,?\s+Required\s+(\d+(?:\.\d+)?)\s+units?',
            re.IGNORECASE
        )

        # Pattern for "- Component: Current stock X units, Q4 requirement Y units" format
        self.stock_requirement_pattern = re.compile(
            r'-\s*([^:]+):\s*Current\s+stock\s+(\d+(?:\.\d+)?)\s+units?,?\s+Q4\s+requirement\s+(\d+(?:\.\d+)?)\s+units?',
            re.IGNORECASE
        )
        self.weight_pattern = re.compile(
            r'(\w+).*?weight\s*:?\s*(\d+(?:\.\d+)?)',
            re.IGNORECASE
        )

        # Additional patterns for financial data
        self.ratio_pattern = re.compile(
            r'(\w+)\s+(?:turnover|ratio)\s*:?\s*(\d+(?:\.\d+)?)',
            re.IGNORECASE
        )
        self.percentage_pattern = re.compile(
            r'(\w+).*?(\d+(?:\.\d+)?)%',
            re.IGNORECASE
        )

        # Industry-specific component mappings
        self.tech_components = {
            'cpu': {'default_weight': 0.2, 'criticality': 'high'},
            'gpu': {'default_weight': 0.3, 'criticality': 'high'},
            'memory': {'default_weight': 0.15, 'criticality': 'medium'},
            'storage': {'default_weight': 0.1, 'criticality': 'medium'},
            'motherboard': {'default_weight': 0.15, 'criticality': 'high'},
            'power_supply': {'default_weight': 0.1, 'criticality': 'medium'}
        }

        self.manufacturing_components = {
            'raw_materials': {'default_weight': 0.4, 'lead_time': 30},
            'work_in_progress': {'default_weight': 0.3, 'lead_time': 15},
            'finished_goods': {'default_weight': 0.3, 'lead_time': 7}
        }

    def extract_from_text(self, financial_text: str) -> Dict[str, Dict[str, float]]:
        """Extract component data from financial text"""
        components = {}

        try:
            # Extract inventory data using original pattern
            for match in self.inventory_pattern.finditer(financial_text):
                component = match.group(1).lower()
                available = float(match.group(2))
                required = float(match.group(3))

                components[component] = {
                    'available': available,
                    'required': required,
                    'weight': 1.0  # Default weight
                }

            # Extract using component name pattern (e.g., "Primary Processors (CPU): Available 180 units, Required 250 units")
            for match in self.component_name_pattern.finditer(financial_text):
                component_name = match.group(1).strip().lower()
                # Clean up component name
                component_name = re.sub(r'[^\w\s]', '', component_name).replace(' ', '_')
                available = float(match.group(2))
                required = float(match.group(3))

                components[component_name] = {
                    'available': available,
                    'required': required,
                    'weight': 1.0  # Default weight
                }

            # Extract using stock requirement pattern (e.g., "- Microprocessors: Current stock 200 units, Q4 requirement 300 units")
            for match in self.stock_requirement_pattern.finditer(financial_text):
                component_name = match.group(1).strip().lower()
                # Clean up component name
                component_name = re.sub(r'[^\w\s]', '', component_name).replace(' ', '_')
                available = float(match.group(2))
                required = float(match.group(3))

                components[component_name] = {
                    'available': available,
                    'required': required,
                    'weight': 1.0  # Default weight
                }

            # Extract weights if present
            weights = {}

            # First try the original weight pattern (e.g., "CPU weight 0.3")
            for match in self.weight_pattern.finditer(financial_text):
                component = match.group(1).lower()
                weight = float(match.group(2))
                weights[component] = weight

            # Also look for "Weight X%" patterns
            weight_percentage_pattern = re.compile(r'Weight\s+(\d+(?:\.\d+)?)%', re.IGNORECASE)

            # Split text into lines and process each component section
            lines = financial_text.split('\n')
            current_component = None

            for line in lines:
                # Check if this line defines a component
                component_match = self.component_name_pattern.search(line)
                if component_match:
                    current_component = component_match.group(1).strip().lower()
                    current_component = re.sub(r'[^\w\s]', '', current_component).replace(' ', '_')

                # Look for weight percentage in the same or following lines
                weight_match = weight_percentage_pattern.search(line)
                if weight_match and current_component:
                    weight_value = float(weight_match.group(1)) / 100.0  # Convert percentage to decimal
                    weights[current_component] = weight_value

            # Apply weights to components
            if weights:
                total_weight = sum(weights.values())
                if total_weight > 0:
                    for component in components:
                        if component in weights:
                            components[component]['weight'] = weights[component] / total_weight
                        else:
                            # Assign remaining weight equally to components without explicit weights
                            remaining_components = [c for c in components if c not in weights]
                            if remaining_components:
                                remaining_weight = (1.0 - sum(weights.values())) / len(remaining_components)
                                components[component]['weight'] = max(0.01, remaining_weight)

        except Exception as e:
            logger.warning(f"Text extraction failed: {e}")

        return components

    def process_structured_data(
        self,
        components: Optional[Dict[str, Dict[str, int]]] = None,
        required_quantities: Optional[List[float]] = None,
        available_quantities: Optional[List[float]] = None,
        weights: Optional[Dict[str, float]] = None
    ) -> Dict[str, Dict[str, float]]:
        """Process structured input data"""

        processed_components = {}

        try:
            # Process components dictionary
            if components:
                for component, data in components.items():
                    processed_components[component] = {
                        'required': float(data.get('required', 0)),
                        'available': float(data.get('available', 0)),
                        'weight': 1.0
                    }

            # Process direct quantity lists
            elif required_quantities and available_quantities:
                min_length = min(len(required_quantities), len(available_quantities))
                for i in range(min_length):
                    component_name = f"component_{i+1}"
                    processed_components[component_name] = {
                        'required': float(required_quantities[i]),
                        'available': float(available_quantities[i]),
                        'weight': 1.0
                    }

            # Apply weights if provided
            if weights and processed_components:
                total_weight = sum(weights.values())
                if total_weight > 0:
                    for component in processed_components:
                        if component in weights:
                            processed_components[component]['weight'] = weights[component] / total_weight
                        else:
                            # Distribute remaining weight equally
                            remaining_components = [c for c in processed_components if c not in weights]
                            if remaining_components:
                                remaining_weight = 1.0 - sum(weights.values()) / total_weight
                                equal_weight = remaining_weight / len(remaining_components)
                                processed_components[component]['weight'] = equal_weight

        except Exception as e:
            logger.error(f"Structured data processing failed: {e}")

        return processed_components

    def validate_and_sanitize(
        self,
        components: Dict[str, Dict[str, float]]
    ) -> Dict[str, Dict[str, float]]:
        """Validate and sanitize component data"""

        sanitized = {}

        for component, data in components.items():
            try:
                required = max(0.0, float(data.get('required', 0)))
                available = max(0.0, float(data.get('available', 0)))
                weight = max(0.0, min(1.0, float(data.get('weight', 1.0))))

                # Skip components with no requirements
                if required > 0:
                    sanitized[component] = {
                        'required': required,
                        'available': available,
                        'weight': weight
                    }

            except (ValueError, TypeError) as e:
                logger.warning(f"Invalid data for component {component}: {e}")
                continue

        # Normalize weights
        if sanitized:
            total_weight = sum(comp['weight'] for comp in sanitized.values())
            if total_weight > 0:
                for component in sanitized:
                    sanitized[component]['weight'] /= total_weight
            else:
                # Equal weights if all are zero
                equal_weight = 1.0 / len(sanitized)
                for component in sanitized:
                    sanitized[component]['weight'] = equal_weight

        return sanitized

    def estimate_from_financial_ratios(
        self,
        financial_data: str,
        company_sector: str = "technology"
    ) -> Dict[str, Dict[str, float]]:
        """Estimate component shortages from financial ratios and industry data"""

        components = {}

        try:
            # Extract financial ratios
            ratios = {}
            for match in self.ratio_pattern.finditer(financial_data):
                ratio_name = match.group(1).lower()
                ratio_value = float(match.group(2))
                ratios[ratio_name] = ratio_value

            # Use sector-specific estimation
            if company_sector.lower() in ["technology", "tech", "hardware"]:
                components = self._estimate_tech_components(ratios)
            elif company_sector.lower() in ["manufacturing", "industrial"]:
                components = self._estimate_manufacturing_components(ratios)
            else:
                # Generic estimation
                components = self._estimate_generic_components(ratios)

        except Exception as e:
            logger.warning(f"Financial ratio estimation failed: {e}")

        return components

    def _estimate_tech_components(self, ratios: Dict[str, float]) -> Dict[str, Dict[str, float]]:
        """Estimate technology sector component shortages"""

        components = {}
        inventory_turnover = ratios.get('inventory_turnover', 6.0)

        # Higher turnover suggests potential shortages
        shortage_factor = min(0.8, max(0.2, (inventory_turnover - 4) / 8))

        for component, defaults in self.tech_components.items():
            base_required = 100 * defaults['default_weight']
            estimated_available = base_required * (1 - shortage_factor)

            components[component] = {
                'required': base_required,
                'available': estimated_available,
                'weight': defaults['default_weight']
            }

        return components

    def _estimate_manufacturing_components(self, ratios: Dict[str, float]) -> Dict[str, Dict[str, float]]:
        """Estimate manufacturing sector component shortages"""

        components = {}
        inventory_turnover = ratios.get('inventory_turnover', 8.0)

        # Manufacturing typically has higher turnover
        shortage_factor = min(0.7, max(0.1, (inventory_turnover - 6) / 10))

        for component, defaults in self.manufacturing_components.items():
            base_required = 100 * defaults['default_weight']

            # Adjust for lead times
            lead_time_factor = defaults['lead_time'] / 30
            adjusted_shortage = shortage_factor * lead_time_factor

            estimated_available = base_required * (1 - adjusted_shortage)

            components[component] = {
                'required': base_required,
                'available': estimated_available,
                'weight': defaults['default_weight']
            }

        return components

    def _estimate_generic_components(self, ratios: Dict[str, float]) -> Dict[str, Dict[str, float]]:
        """Generic component estimation for unknown sectors"""

        components = {}
        inventory_turnover = ratios.get('inventory_turnover', 5.0)

        # Conservative estimation
        shortage_factor = min(0.5, max(0.1, (inventory_turnover - 3) / 6))

        # Create generic components
        generic_components = {
            'primary_inventory': {'weight': 0.4},
            'secondary_inventory': {'weight': 0.3},
            'support_inventory': {'weight': 0.3}
        }

        for component, defaults in generic_components.items():
            base_required = 100 * defaults['weight']
            estimated_available = base_required * (1 - shortage_factor)

            components[component] = {
                'required': base_required,
                'available': estimated_available,
                'weight': defaults['weight']
            }

        return components

    def assess_data_quality(self, components: Dict[str, Dict[str, float]]) -> Dict[str, Any]:
        """Assess the quality of extracted component data"""

        quality_metrics = {
            'completeness': 0.0,
            'accuracy': 0.0,
            'consistency': 0.0,
            'confidence': 0.0,
            'data_source': 'unknown'
        }

        if not components:
            return quality_metrics

        try:
            # Completeness: presence of required fields
            complete_components = 0
            for component, data in components.items():
                if all(key in data for key in ['required', 'available', 'weight']):
                    complete_components += 1

            quality_metrics['completeness'] = complete_components / len(components)

            # Accuracy: reasonable value ranges
            accurate_components = 0
            for component, data in components.items():
                if (data.get('required', 0) >= 0 and
                    data.get('available', 0) >= 0 and
                    0 <= data.get('weight', 0) <= 1):
                    accurate_components += 1

            quality_metrics['accuracy'] = accurate_components / len(components)

            # Consistency: logical relationships
            consistent_components = 0
            for component, data in components.items():
                required = data.get('required', 0)
                available = data.get('available', 0)
                # Available should not exceed required by too much (some buffer is normal)
                if available <= required * 1.5:  # Allow 50% buffer
                    consistent_components += 1

            quality_metrics['consistency'] = consistent_components / len(components)

            # Overall confidence
            quality_metrics['confidence'] = (
                quality_metrics['completeness'] * 0.4 +
                quality_metrics['accuracy'] * 0.3 +
                quality_metrics['consistency'] * 0.3
            )

            # Determine data source quality
            if quality_metrics['confidence'] > 0.8:
                quality_metrics['data_source'] = 'high_quality'
            elif quality_metrics['confidence'] > 0.6:
                quality_metrics['data_source'] = 'medium_quality'
            else:
                quality_metrics['data_source'] = 'low_quality'

        except Exception as e:
            logger.error(f"Data quality assessment failed: {e}")

        return quality_metrics


class ShortageErrorHandler:
    """Comprehensive error handling for shortage analysis operations"""

    def __init__(self):
        self.error_counts = {}
        self.last_errors = {}

    def handle_data_processing_error(
        self,
        error: Exception,
        context: Dict[str, Any]
    ) -> Dict[str, Dict[str, float]]:
        """Handle data processing errors with fallback strategies"""

        error_type = type(error).__name__
        self._log_error("data_processing", error_type, str(error), context)

        # Provide fallback data based on error type
        if "parsing" in str(error).lower():
            logger.warning("Data parsing failed, using conservative estimates")
            return {
                "estimated_component": {
                    "required": 1.0,
                    "available": 0.7,
                    "weight": 1.0
                }
            }
        elif "validation" in str(error).lower():
            logger.warning("Data validation failed, using minimal viable data")
            return {
                "default_component": {
                    "required": 1.0,
                    "available": 0.5,
                    "weight": 1.0
                }
            }
        else:
            logger.error(f"Unexpected data processing error: {error}")
            return {
                "fallback_component": {
                    "required": 1.0,
                    "available": 0.3,
                    "weight": 1.0
                }
            }

    def handle_mcp_service_error(
        self,
        error: Exception,
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Handle MCP service errors with appropriate fallbacks"""

        error_type = type(error).__name__
        self._log_error("mcp_service", error_type, str(error), context)

        # Determine fallback strategy based on error type
        if "connection" in str(error).lower() or "timeout" in str(error).lower():
            logger.warning("MCP service connection failed, using local calculations")
            return {
                "service_available": False,
                "shortage_index": 0.6,  # Conservative estimate
                "calculation_method": "fallback_connection_error",
                "errors": [f"Connection error: {str(error)}"]
            }
        elif "validation" in str(error).lower():
            logger.warning("MCP service validation failed, adjusting input")
            return {
                "service_available": False,
                "shortage_index": 0.5,
                "calculation_method": "fallback_validation_error",
                "errors": [f"Validation error: {str(error)}"]
            }
        else:
            logger.error(f"Unexpected MCP service error: {error}")
            return {
                "service_available": False,
                "shortage_index": 0.7,  # Higher conservative estimate for unknown errors
                "calculation_method": "fallback_unknown_error",
                "errors": [f"Unknown error: {str(error)}"]
            }

    def handle_analysis_error(
        self,
        error: Exception,
        context: Dict[str, Any]
    ) -> ShortageAnalysisOutputSchema:
        """Handle analysis errors while maintaining schema compliance"""

        error_type = type(error).__name__
        self._log_error("analysis", error_type, str(error), context)

        company_name = context.get('company_name', 'Unknown Company')

        # Create error-appropriate response
        if "schema" in str(error).lower() or "validation" in str(error).lower():
            return ShortageAnalysisOutputSchema(
                company_name=company_name,
                shortage_index=0.5,
                risk_level="MEDIUM",
                shortage_analysis=f"Schema validation error prevented complete analysis: {str(error)}",
                recommendations=[
                    "Verify input data format and structure",
                    "Check required fields are present",
                    "Retry analysis with corrected data"
                ],
                response=f"Analysis for {company_name} failed due to data validation issues. Please review input format."
            )
        elif "timeout" in str(error).lower():
            return ShortageAnalysisOutputSchema(
                company_name=company_name,
                shortage_index=0.7,
                risk_level="HIGH",
                shortage_analysis=f"Analysis timeout suggests potential service overload: {str(error)}",
                recommendations=[
                    "Retry analysis during off-peak hours",
                    "Consider breaking analysis into smaller components",
                    "Check system resource availability"
                ],
                response=f"Analysis for {company_name} timed out. System may be experiencing high load."
            )
        else:
            return ShortageAnalysisOutputSchema(
                company_name=company_name,
                shortage_index=0.6,
                risk_level="MEDIUM",
                shortage_analysis=f"Unexpected error during analysis: {str(error)}",
                recommendations=[
                    "Contact system administrator",
                    "Review error logs for detailed information",
                    "Retry analysis after system check"
                ],
                response=f"Analysis for {company_name} encountered an unexpected error. Please contact support."
            )

    def _log_error(
        self,
        category: str,
        error_type: str,
        error_message: str,
        context: Dict[str, Any]
    ) -> None:
        """Log error with structured information"""

        # Track error frequency
        error_key = f"{category}_{error_type}"
        self.error_counts[error_key] = self.error_counts.get(error_key, 0) + 1
        self.last_errors[error_key] = {
            'message': error_message,
            'context': context,
            'timestamp': datetime.now().isoformat()
        }

        # Log with appropriate level
        if self.error_counts[error_key] == 1:
            logger.error(
                f"First occurrence of {category} error",
                extra={
                    'error_type': error_type,
                    'error_message': error_message,
                    'context': context,
                    'category': category
                }
            )
        elif self.error_counts[error_key] <= 5:
            logger.warning(
                f"Recurring {category} error (count: {self.error_counts[error_key]})",
                extra={
                    'error_type': error_type,
                    'error_message': error_message,
                    'context': context,
                    'category': category
                }
            )
        else:
            # Reduce log noise for frequent errors
            logger.debug(
                f"Frequent {category} error (count: {self.error_counts[error_key]})",
                extra={
                    'error_type': error_type,
                    'category': category
                }
            )

    def get_error_summary(self) -> Dict[str, Any]:
        """Get summary of errors encountered"""

        return {
            'error_counts': self.error_counts.copy(),
            'recent_errors': {
                key: {
                    'message': value['message'],
                    'timestamp': value['timestamp']
                }
                for key, value in self.last_errors.items()
            },
            'total_errors': sum(self.error_counts.values())
        }


class ShortageSchemaValidator:
    """Validate and sanitize shortage analysis schemas"""

    def __init__(self):
        self.validation_errors = []

    def validate_input(
        self,
        input_data: Any
    ) -> ShortageAnalysisInputSchema:
        """Validate and convert input to proper schema"""

        self.validation_errors.clear()

        try:
            # Handle different input types
            if isinstance(input_data, ShortageAnalysisInputSchema):
                validated_input = input_data
            elif isinstance(input_data, dict):
                validated_input = ShortageAnalysisInputSchema(**input_data)
            else:
                # Try to convert to dict first
                if hasattr(input_data, 'model_dump'):
                    validated_input = ShortageAnalysisInputSchema(**input_data.model_dump())
                elif hasattr(input_data, 'dict'):
                    validated_input = ShortageAnalysisInputSchema(**input_data.dict())
                else:
                    raise ValueError(f"Unsupported input type: {type(input_data)}")

            # Additional validation
            self._validate_input_constraints(validated_input)

            logger.info("Input schema validation successful")
            return validated_input

        except Exception as e:
            error_msg = f"Input validation failed: {str(e)}"
            self.validation_errors.append(error_msg)
            logger.error(error_msg)

            # Try to preserve original company name
            original_company_name = "validation_error"
            if hasattr(input_data, 'company_name'):
                original_company_name = input_data.company_name
            elif isinstance(input_data, dict) and 'company_name' in input_data:
                original_company_name = input_data['company_name']

            # Return minimal valid schema with preserved company name
            return ShortageAnalysisInputSchema(
                company_name=original_company_name,
                message=f"Input validation error: {str(e)}",
                context="Using default values due to validation failure"
            )

    def validate_output(
        self,
        output_data: Dict[str, Any]
    ) -> ShortageAnalysisOutputSchema:
        """Validate output data against schema"""

        try:
            # Ensure required fields are present with valid values
            validated_data = {
                'company_name': str(output_data.get('company_name', 'Unknown')),
                'shortage_index': self._validate_shortage_index(
                    output_data.get('shortage_index', 0.5)
                ),
                'risk_level': self._validate_risk_level(
                    output_data.get('risk_level', 'MEDIUM')
                ),
                'shortage_analysis': str(output_data.get('shortage_analysis', 'No analysis available')),
                'recommendations': self._validate_recommendations(
                    output_data.get('recommendations', [])
                ),
                'response': str(output_data.get('response', 'No response available'))
            }

            validated_output = ShortageAnalysisOutputSchema(**validated_data)
            logger.info("Output schema validation successful")
            return validated_output

        except Exception as e:
            error_msg = f"Output validation failed: {str(e)}"
            self.validation_errors.append(error_msg)
            logger.error(error_msg)

            # Return minimal valid output
            return ShortageAnalysisOutputSchema(
                company_name=str(output_data.get('company_name', 'Unknown')),
                shortage_index=0.5,
                risk_level="MEDIUM",
                shortage_analysis=f"Output validation error: {str(e)}",
                recommendations=["Review data validation", "Check output format"],
                response="Analysis completed with validation errors"
            )

    def _validate_input_constraints(self, input_data: ShortageAnalysisInputSchema) -> None:
        """Validate additional input constraints"""

        # Check quantity list consistency
        if (input_data.required_quantities is not None and
            input_data.available_quantities is not None):

            if len(input_data.required_quantities) != len(input_data.available_quantities):
                raise ValueError("Required and available quantities must have same length")

            if any(q < 0 for q in input_data.required_quantities + input_data.available_quantities):
                raise ValueError("Quantities must be non-negative")

        # Check components structure
        if input_data.components:
            for component, data in input_data.components.items():
                if not isinstance(data, dict):
                    raise ValueError(f"Component {component} data must be a dictionary")

                required_keys = ['required', 'available']
                missing_keys = [key for key in required_keys if key not in data]
                if missing_keys:
                    raise ValueError(f"Component {component} missing keys: {missing_keys}")

        # Check weights
        if input_data.weights:
            weight_sum = sum(input_data.weights.values())
            if abs(weight_sum - 1.0) > 0.1:  # Allow some tolerance
                logger.warning(f"Weights sum to {weight_sum}, should be close to 1.0")

    def _validate_shortage_index(self, value: Any) -> float:
        """Validate shortage index value"""

        try:
            shortage_index = float(value)
            # Clamp to valid range
            return max(0.0, min(1.0, shortage_index))
        except (ValueError, TypeError):
            logger.warning(f"Invalid shortage index value: {value}, using default 0.5")
            return 0.5

    def _validate_risk_level(self, value: Any) -> str:
        """Validate risk level value"""

        valid_levels = ["LOW", "MEDIUM", "HIGH"]

        if isinstance(value, str) and value.upper() in valid_levels:
            return value.upper()
        else:
            logger.warning(f"Invalid risk level: {value}, using default MEDIUM")
            return "MEDIUM"

    def _validate_recommendations(self, value: Any) -> List[str]:
        """Validate recommendations list"""

        try:
            if isinstance(value, list):
                # Convert all items to strings and filter empty ones
                recommendations = [str(item).strip() for item in value if str(item).strip()]
                return recommendations[:10]  # Limit to 10 recommendations
            else:
                logger.warning(f"Invalid recommendations format: {type(value)}, using default")
                return ["Review analysis results", "Consider risk mitigation strategies"]
        except Exception as e:
            logger.error(f"Recommendations validation failed: {e}")
            return ["Review analysis results"]

    def get_validation_errors(self) -> List[str]:
        """Get list of validation errors"""
        return self.validation_errors.copy()

    def has_validation_errors(self) -> bool:
        """Check if there are validation errors"""
        return len(self.validation_errors) > 0


class ShortageRiskAnalyzer:
    """Analyze shortage risks and generate recommendations"""

    def __init__(self, custom_thresholds: Optional[Dict[str, float]] = None):
        # Default risk thresholds (configurable)
        self.risk_thresholds = {
            'low': 0.3,
            'medium': 0.7,
            'high': 1.0
        }

        # Apply custom thresholds if provided
        if custom_thresholds:
            self.risk_thresholds.update(custom_thresholds)
            logger.info(f"Using custom risk thresholds: {self.risk_thresholds}")

        # Business rules for risk classification
        self.business_rules = {
            'critical_components': ['cpu', 'gpu', 'motherboard'],  # Always high priority
            'seasonal_adjustment': 0.1,  # Adjust thresholds during peak seasons
            'supplier_reliability_factor': 0.05,  # Adjust based on supplier history
            'lead_time_factor': 0.02  # Adjust based on component lead times
        }

    def classify_risk_level(
        self,
        shortage_index: float,
        components: Optional[Dict[str, Dict[str, float]]] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> str:
        """Enhanced risk level classification with business rules"""

        # Base classification
        base_risk = self._get_base_risk_level(shortage_index)

        # Apply business rules adjustments
        adjusted_risk = self._apply_business_rules(
            base_risk, shortage_index, components, context
        )

        logger.info(f"Risk classification: base={base_risk}, adjusted={adjusted_risk}, index={shortage_index:.3f}")
        return adjusted_risk

    def _get_base_risk_level(self, shortage_index: float) -> str:
        """Get base risk level from shortage index"""
        if shortage_index <= self.risk_thresholds['low']:
            return "LOW"
        elif shortage_index <= self.risk_thresholds['medium']:
            return "MEDIUM"
        else:
            return "HIGH"

    def _apply_business_rules(
        self,
        base_risk: str,
        shortage_index: float,
        components: Optional[Dict[str, Dict[str, float]]],
        context: Optional[Dict[str, Any]]
    ) -> str:
        """Apply business rules to adjust risk classification"""

        risk_score = self._risk_to_score(base_risk)
        adjustments = []

        # Critical component adjustment
        if components:
            critical_shortage = self._assess_critical_components(components)
            if critical_shortage > 0.5:
                risk_score += 1
                adjustments.append("critical_components")

        # Seasonal adjustment
        if context and context.get('season') == 'peak':
            risk_score += 0.5
            adjustments.append("seasonal_peak")

        # Supplier reliability adjustment
        if context and context.get('supplier_reliability', 1.0) < 0.8:
            risk_score += 0.3
            adjustments.append("supplier_reliability")

        # Lead time adjustment
        if components:
            avg_lead_time = self._estimate_average_lead_time(components)
            if avg_lead_time > 30:  # days
                risk_score += 0.2
                adjustments.append("long_lead_time")

        # Convert back to risk level
        final_risk = self._score_to_risk(risk_score)

        if adjustments:
            logger.info(f"Risk adjustments applied: {adjustments}")

        return final_risk

    def _assess_critical_components(self, components: Dict[str, Dict[str, float]]) -> float:
        """Assess shortage level for critical components"""

        critical_shortages = []

        for component, data in components.items():
            if component.lower() in self.business_rules['critical_components']:
                required = data.get('required', 0)
                available = data.get('available', 0)

                if required > 0:
                    shortage = (required - available) / required
                    critical_shortages.append(max(0, shortage))

        return max(critical_shortages) if critical_shortages else 0

    def _estimate_average_lead_time(self, components: Dict[str, Dict[str, float]]) -> float:
        """Estimate average lead time for components"""

        # Simplified lead time estimation based on component types
        lead_times = []

        for component in components.keys():
            if 'cpu' in component.lower() or 'gpu' in component.lower():
                lead_times.append(45)  # High-tech components have longer lead times
            elif 'memory' in component.lower() or 'storage' in component.lower():
                lead_times.append(30)
            else:
                lead_times.append(20)  # Generic components

        return sum(lead_times) / len(lead_times) if lead_times else 20

    def _risk_to_score(self, risk_level: str) -> float:
        """Convert risk level to numeric score for calculations"""
        risk_scores = {"LOW": 1.0, "MEDIUM": 2.0, "HIGH": 3.0}
        return risk_scores.get(risk_level, 2.0)

    def _score_to_risk(self, score: float) -> str:
        """Convert numeric score back to risk level"""
        if score <= 1.5:
            return "LOW"
        elif score <= 2.5:
            return "MEDIUM"
        else:
            return "HIGH"

    def get_risk_explanation(
        self,
        shortage_index: float,
        risk_level: str,
        components: Optional[Dict[str, Dict[str, float]]] = None
    ) -> str:
        """Generate explanation for risk classification"""

        explanations = []

        # Base explanation
        if risk_level == "HIGH":
            explanations.append(f"High shortage index ({shortage_index:.3f}) indicates critical supply shortages")
        elif risk_level == "MEDIUM":
            explanations.append(f"Moderate shortage index ({shortage_index:.3f}) suggests supply chain stress")
        else:
            explanations.append(f"Low shortage index ({shortage_index:.3f}) indicates adequate supply levels")

        # Component-specific explanations
        if components:
            critical_components = [
                comp for comp in components.keys()
                if comp.lower() in self.business_rules['critical_components']
            ]

            if critical_components:
                explanations.append(f"Critical components affected: {', '.join(critical_components)}")

        return ". ".join(explanations)

    def generate_analysis(
        self,
        shortage_index: float,
        weighted_shortage_index: Optional[float],
        components: Dict[str, Dict[str, float]],
        mcp_results: Dict[str, Any]
    ) -> str:
        """Generate detailed shortage analysis"""

        analysis_parts = [
            f"Shortage Index Analysis: {shortage_index:.3f}",
            ""
        ]

        if weighted_shortage_index is not None:
            analysis_parts.append(f"Weighted Shortage Index: {weighted_shortage_index:.3f}")
            analysis_parts.append("")

        analysis_parts.append("Component Analysis:")

        # Analyze individual components
        for component, data in components.items():
            required = data['required']
            available = data['available']
            weight = data['weight']

            component_shortage = max(0, (required - available) / required) if required > 0 else 0

            analysis_parts.append(
                f"- {component}: {available:.1f}/{required:.1f} available "
                f"(shortage: {component_shortage:.1%}, weight: {weight:.1%})"
            )

        # Add MCP tool results if available
        if mcp_results:
            analysis_parts.extend([
                "",
                "MCP Tool Results:",
                f"- Service status: {mcp_results.get('service_status', 'Unknown')}",
                f"- Calculation method: {mcp_results.get('calculation_method', 'Unknown')}"
            ])

        return "\n".join(analysis_parts)

    def generate_recommendations(
        self,
        risk_level: str,
        shortage_index: float,
        components: Dict[str, Dict[str, float]]
    ) -> List[str]:
        """Generate actionable recommendations based on risk level"""

        recommendations = []

        # Risk-level specific recommendations
        if risk_level == "HIGH":
            recommendations.extend([
                "URGENT: Immediate action required to address critical shortages",
                "Activate emergency procurement procedures",
                "Consider alternative suppliers or substitute materials",
                "Implement demand management and allocation strategies",
                "Escalate to senior management for resource allocation"
            ])
        elif risk_level == "MEDIUM":
            recommendations.extend([
                "Monitor shortage levels closely with daily reviews",
                "Increase safety stock for critical components",
                "Review supplier performance and delivery reliability",
                "Consider diversifying supply sources to reduce risk",
                "Implement early warning systems for inventory levels"
            ])
        else:  # LOW
            recommendations.extend([
                "Maintain current inventory management practices",
                "Continue regular supplier relationship management",
                "Monitor market conditions for potential supply disruptions",
                "Review inventory optimization opportunities"
            ])

        # Component-specific recommendations
        critical_components = [
            comp for comp, data in components.items()
            if (data['required'] - data['available']) / data['required'] > 0.5
        ]

        if critical_components:
            recommendations.append(
                f"Priority focus on critical components: {', '.join(critical_components)}"
            )

        # High-weight component recommendations
        high_weight_components = [
            comp for comp, data in components.items()
            if data['weight'] > 0.3 and (data['required'] - data['available']) / data['required'] > 0.2
        ]

        if high_weight_components:
            recommendations.append(
                f"Strategic attention needed for high-impact components: {', '.join(high_weight_components)}"
            )

        # Add data quality-based recommendations
        if len(components) > 1:  # Multiple components indicate good data quality
            recommendations.extend([
                "Leverage detailed component analysis for strategic planning",
                "Implement component-specific inventory optimization strategies"
            ])

        # Add general best practice recommendations for comprehensive analysis
        if len(recommendations) < 5:  # Ensure minimum recommendation count
            additional_recommendations = [
                "Establish regular supply chain risk assessment procedures",
                "Develop contingency plans for supply disruptions",
                "Consider implementing just-in-time inventory management",
                "Evaluate supplier diversification opportunities",
                "Monitor industry trends and market conditions"
            ]

            # Add recommendations until we have at least 5
            for rec in additional_recommendations:
                if rec not in recommendations and len(recommendations) < 6:
                    recommendations.append(rec)

        return recommendations


def create_shortage_analyzer_agent(company_name: str = "test_company") -> BaseAgentWrapper:
    """
    Create an enhanced shortage analyzer agent for supply chain risk assessment.

    This agent integrates with the agent_develop shortage-index server to calculate
    shortage indices and analyze supply chain risks based on financial data.

    Args:
        company_name: Name of the company being analyzed

    Returns:
        BaseAgentWrapper configured for enhanced shortage analysis
    """

    instruction = f"""You are an Enhanced Shortage Analyzer Agent specializing in comprehensive supply chain risk assessment for {company_name}.

CORE RESPONSIBILITIES:
1. Analyze financial data to identify inventory shortages and supply chain vulnerabilities
2. Extract and process required_qty, available_qty, and weight data from multiple sources
3. Utilize MCP shortage-index server tools (ShortageIndexTool and WeightedShortageIndexTool)
4. Provide detailed risk assessments with quantitative shortage indices
5. Generate actionable, prioritized recommendations for supply chain optimization

ADVANCED ANALYSIS FRAMEWORK:

Data Processing Capabilities:
- Parse structured financial text for inventory patterns
- Extract component data from various formats (text, structured data, direct quantities)
- Handle multiple input sources: financial_data, components dict, quantity lists
- Apply intelligent data validation and sanitization
- Support weighted analysis for strategic component prioritization

MCP Tool Integration:
- Use ShortageIndexTool for basic shortage calculations: shortage_index = 1 - min(available_qty[i] / required_qty[i])
- Use WeightedShortageIndexTool for strategic analysis: weighted_shortage = sum(weight[i] * shortage[i])
- Implement fallback calculations when MCP services unavailable
- Handle tool errors gracefully with comprehensive error recovery

Risk Classification System:
- LOW Risk (0.0-0.3): Adequate inventory levels, minimal supply chain concerns
- MEDIUM Risk (0.3-0.7): Moderate shortages, requires monitoring and proactive measures
- HIGH Risk (0.7-1.0): Critical shortages, immediate action required

Advanced Output Generation:
- Provide detailed component-by-component analysis
- Include both simple and weighted shortage indices when applicable
- Generate risk-appropriate recommendations (urgent vs. preventive)
- Cite calculation methods and data sources for transparency
- Maintain professional tone with quantitative backing

Error Handling and Resilience:
- Gracefully handle MCP service unavailability with local calculations
- Validate and sanitize all input data before processing
- Provide meaningful error messages and fallback responses
- Log processing steps for debugging and improvement

Data Source Flexibility:
- Financial text parsing: "CPU available is 1, require is 2, weight 0.2"
- Structured components: {{"cpu": {{"required": 2, "available": 1}}}}
- Direct quantity lists: required_quantities=[2,4], available_quantities=[1,2]
- Mixed data sources with intelligent merging and validation

Quality Assurance:
- Normalize weights to sum to 1.0 for weighted calculations
- Ensure non-negative quantities and valid ranges
- Cross-validate results between different calculation methods
- Provide confidence indicators for analysis quality

Always maintain analytical rigor, provide data-driven insights, and ensure actionable recommendations aligned with risk levels."""

    # Create enhanced agent with comprehensive configuration
    agent = create_enhanced_agent(
        name=f"enhanced_shortage_analyzer_{company_name.lower().replace(' ', '_')}",
        instruction=instruction,
        server_names=["shortage-index", "fetch"],
        model="Qwen/Qwen3-32B"  # Use vLLM with Qwen model for enhanced reasoning
    )

    # Add enhanced processing capabilities
    agent.data_processor = ShortageDataProcessor()
    agent.risk_analyzer = ShortageRiskAnalyzer()
    agent.error_handler = ShortageErrorHandler()
    agent.schema_validator = ShortageSchemaValidator()

    # Add custom processing method
    async def enhanced_shortage_analysis(input_data: Any) -> ShortageAnalysisOutputSchema:
        """Enhanced shortage analysis with comprehensive processing"""

        try:
            # Validate input schema
            validated_input = agent.schema_validator.validate_input(input_data)
            logger.info(f"Starting shortage analysis for {validated_input.company_name}")

            # Process input data
            components = {}

            # Extract from financial text if provided
            if validated_input.financial_data:
                text_components = agent.data_processor.extract_from_text(validated_input.financial_data)
                components.update(text_components)

            # Process structured data
            structured_components = agent.data_processor.process_structured_data(
                components=validated_input.components,
                required_quantities=validated_input.required_quantities,
                available_quantities=validated_input.available_quantities,
                weights=validated_input.weights
            )
            components.update(structured_components)

            # Validate and sanitize
            components = agent.data_processor.validate_and_sanitize(components)

            if not components:
                logger.error("No valid components found, cannot proceed with MCP analysis")
                raise RuntimeError("No valid components extracted from input data")

            # Prepare data for MCP tools
            required_qty = [comp['required'] for comp in components.values()]
            available_qty = [comp['available'] for comp in components.values()]
            weights = [comp['weight'] for comp in components.values()]

            # Execute MCP tool analysis
            mcp_integration = MCPToolIntegration(agent)
            analysis_results = await mcp_integration.execute_shortage_analysis(
                required_qty, available_qty, weights
            )

            # Extract results
            shortage_index = analysis_results.get("shortage_index", 0.5)
            weighted_shortage_index = analysis_results.get("weighted_shortage_index")

            mcp_results = {
                "service_status": "available" if analysis_results["service_available"] else "unavailable",
                "calculation_method": analysis_results["calculation_method"],
                "errors": analysis_results.get("errors", [])
            }

            if analysis_results["errors"]:
                logger.warning(f"MCP analysis had errors: {analysis_results['errors']}")
            else:
                logger.info("MCP shortage analysis completed successfully")

            # Generate analysis and recommendations with enhanced risk classification
            analysis_context = {
                'season': 'normal',  # Could be determined from date or input
                'supplier_reliability': 0.85,  # Could be from historical data
                'market_conditions': 'stable'
            }

            risk_level = agent.risk_analyzer.classify_risk_level(
                shortage_index, components, analysis_context
            )

            analysis_text = agent.risk_analyzer.generate_analysis(
                shortage_index, weighted_shortage_index, components, mcp_results
            )

            recommendations = agent.risk_analyzer.generate_recommendations(
                risk_level, shortage_index, components
            )

            # Generate comprehensive response
            response_parts = [
                f"Shortage Analysis Report for {validated_input.company_name}",
                "=" * (len(validated_input.company_name) + 30),
                "",
                f"Overall Shortage Index: {shortage_index:.3f}",
                f"Risk Level: {risk_level}",
                ""
            ]

            if weighted_shortage_index is not None:
                response_parts.append(f"Weighted Shortage Index: {weighted_shortage_index:.3f}")
                response_parts.append("")

            response_parts.extend([
                "Analysis:",
                analysis_text,
                "",
                "Recommendations:"
            ])

            for i, rec in enumerate(recommendations, 1):
                response_parts.append(f"{i}. {rec}")

            response = "\n".join(response_parts)

            # Create output data and validate
            output_data = {
                'company_name': validated_input.company_name,
                'shortage_index': shortage_index,
                'risk_level': risk_level,
                'shortage_analysis': analysis_text,
                'recommendations': recommendations,
                'response': response
            }

            # Validate output schema
            validated_output = agent.schema_validator.validate_output(output_data)
            return validated_output

        except Exception as e:
            logger.error(f"Enhanced shortage analysis failed: {e}")

            # Use error handler for structured error response
            context = {
                'company_name': getattr(input_data, 'company_name', 'Unknown'),
                'input_data': str(input_data),
                'analysis_stage': 'main_analysis'
            }

            return agent.error_handler.handle_analysis_error(e, context)

    # Attach enhanced processing method
    agent.enhanced_shortage_analysis = enhanced_shortage_analysis
    
    # Ensure LLM is initialized for MCP operations
    async def initialize_agent_llm():
        """Initialize the agent's LLM for MCP operations."""
        if hasattr(agent, 'ensure_llm_initialized'):
            await agent.ensure_llm_initialized()
    
    # Add initialization method to agent
    agent.initialize_llm = initialize_agent_llm

    logger.info(f"Enhanced shortage analyzer agent created for {company_name}")
    return agent