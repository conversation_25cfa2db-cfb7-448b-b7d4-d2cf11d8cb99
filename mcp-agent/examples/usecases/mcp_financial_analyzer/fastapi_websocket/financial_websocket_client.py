#!/usr/bin/env python3
"""
Financial WebSocket Client for testing the MCP Financial Analyzer WebSocket endpoints.
"""

import asyncio
import json
import websockets
import argparse
from typing import Optional
from urllib.parse import quote


class FinancialWebSocketClient:
    """Client for testing financial analysis WebSocket endpoints."""
    
    def __init__(self, host: str = "localhost", port: int = 8000):
        self.host = host
        self.port = port
        self.websocket: Optional[websockets.WebSocketServerProtocol] = None
        
    async def connect(self, endpoint: str, user_id: str, company: str = "Apple Inc."):
        """Connect to a specific financial analysis endpoint."""
        encoded_company = quote(company)
        uri = f"ws://{self.host}:{self.port}/ws/{endpoint}/{user_id}?company={encoded_company}"
        print(f"Connecting to {uri}")
        
        try:
            self.websocket = await websockets.connect(uri)
            print(f"✅ Connected to {endpoint} endpoint for {company}")
            return True
        except Exception as e:
            print(f"❌ Failed to connect: {e}")
            return False
    
    async def send_message(self, message: str):
        """Send a message to the connected WebSocket."""
        if not self.websocket:
            print("❌ Not connected to any endpoint")
            return
            
        try:
            await self.websocket.send(json.dumps({"message": message}))
            print(f"📤 Sent: {message}")
        except Exception as e:
            print(f"❌ Failed to send message: {e}")
    
    async def listen_for_messages(self):
        """Listen for messages from the WebSocket server."""
        if not self.websocket:
            print("❌ Not connected to any endpoint")
            return
            
        try:
            async for message in self.websocket:
                data = json.loads(message)
                message_type = data.get("type", "unknown")
                content = data.get("message", "")
                
                # Format output based on message type
                if message_type == "system":
                    print(f"🔧 SYSTEM: {content}")
                elif message_type == "progress":
                    print(f"⏳ PROGRESS: {content}")
                elif message_type == "result":
                    print(f"✅ RESULT: {content}")
                elif message_type == "error":
                    print(f"❌ ERROR: {content}")
                else:
                    print(f"📨 {message_type.upper()}: {content}")
                    
        except websockets.exceptions.ConnectionClosed:
            print("🔌 Connection closed")
        except Exception as e:
            print(f"❌ Error listening for messages: {e}")
    
    async def disconnect(self):
        """Disconnect from the WebSocket."""
        if self.websocket:
            await self.websocket.close()
            print("🔌 Disconnected")
    
    async def interactive_session(self, endpoint: str, user_id: str, company: str):
        """Run an interactive session with the specified endpoint."""
        print(f"\n🚀 Starting interactive session with {endpoint} endpoint")
        print(f"👤 User ID: {user_id}")
        print(f"🏢 Company: {company}")
        print("💡 Type 'quit' or 'exit' to end the session\n")
        
        # Connect to the endpoint
        if not await self.connect(endpoint, user_id, company):
            return
        
        # Start listening for messages in the background
        listen_task = asyncio.create_task(self.listen_for_messages())
        
        try:
            while True:
                # Get user input
                try:
                    user_input = input("💬 Enter message: ").strip()
                except (EOFError, KeyboardInterrupt):
                    break
                
                if user_input.lower() in ['quit', 'exit']:
                    break
                
                if user_input:
                    await self.send_message(user_input)
                    # Give some time for the response
                    await asyncio.sleep(0.1)
        
        finally:
            listen_task.cancel()
            await self.disconnect()


async def test_endpoint(endpoint: str, company: str = "Apple Inc.", user_id: str = "test_user"):
    """Test a specific endpoint with a sample message."""
    client = FinancialWebSocketClient()
    
    print(f"\n🧪 Testing {endpoint} endpoint with {company}")
    
    if await client.connect(endpoint, user_id, company):
        # Start listening for messages
        listen_task = asyncio.create_task(client.listen_for_messages())
        
        # Send a test message based on endpoint type
        test_messages = {
            "research": f"Please research the latest financial information for {company}",
            "analyze": f"Analyze the financial performance of {company}",
            "report": f"Generate a financial report for {company}",
            "full_analysis": f"Perform a complete financial analysis of {company}"
        }
        
        test_message = test_messages.get(endpoint, f"Analyze {company}")
        await client.send_message(test_message)
        
        # Wait for response
        await asyncio.sleep(10)
        
        listen_task.cancel()
        await client.disconnect()


async def main():
    """Main function to run the client."""
    parser = argparse.ArgumentParser(description="Financial WebSocket Client")
    parser.add_argument("--endpoint", choices=["research", "analyze", "report", "full_analysis"], 
                       default="research", help="Endpoint to connect to")
    parser.add_argument("--company", default="Apple Inc.", help="Company to analyze")
    parser.add_argument("--user-id", default="test_user", help="User ID for the session")
    parser.add_argument("--interactive", action="store_true", help="Run in interactive mode")
    parser.add_argument("--test-all", action="store_true", help="Test all endpoints")
    parser.add_argument("--host", default="localhost", help="WebSocket server host")
    parser.add_argument("--port", type=int, default=8000, help="WebSocket server port")
    
    args = parser.parse_args()
    
    if args.test_all:
        print("🧪 Testing all endpoints...")
        endpoints = ["research", "analyze", "report", "full_analysis"]
        for endpoint in endpoints:
            await test_endpoint(endpoint, args.company, f"{args.user_id}_{endpoint}")
            await asyncio.sleep(2)  # Brief pause between tests
    elif args.interactive:
        client = FinancialWebSocketClient(args.host, args.port)
        await client.interactive_session(args.endpoint, args.user_id, args.company)
    else:
        await test_endpoint(args.endpoint, args.company, args.user_id)


if __name__ == "__main__":
    print("🔗 MCP Financial Analyzer WebSocket Client")
    print("=" * 50)
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
