"""
Schema definitions for BaseAgent wrapper integration.
Provides input/output schemas for instruction-based agents.
"""

from pydantic import Field, BaseModel
from typing import Optional, List, Dict, Any

try:
    from atomic_agents.lib.base.base_io_schema import BaseIOSchema
except ImportError:
    # Fallback for testing without atomic-agents installed
    class BaseIOSchema(BaseModel):
        """Fallback BaseIOSchema for testing"""
        pass


class InstructionInputSchema(BaseIOSchema):
    """
    Generic input schema for instruction-based agents.
    Captures the message and optional context for processing.
    """
    message: str = Field(
        ..., 
        description="Input message or task for the agent to process"
    )
    context: Optional[str] = Field(
        None, 
        description="Additional context information to help with processing"
    )


class InstructionOutputSchema(BaseIOSchema):
    """
    Generic output schema for instruction-based agents.
    Provides the response and optional reasoning.
    """
    response: str = Field(
        ..., 
        description="Agent's response to the input message"
    )
    reasoning: Optional[str] = Field(
        None, 
        description="Agent's reasoning process or explanation"
    )


class MySQLAnalysisInputSchema(BaseIOSchema):
    """Input schema for MySQL analysis operations."""
    company_name: str = Field(..., description="Name of the company being analyzed")
    query_type: str = Field(default="supplier_reliability", description="Type of historical query to perform")
    material_code: Optional[str] = Field(None, description="Specific material code to query")
    order_number: Optional[str] = Field(None, description="Specific order number to query")
    message: str = Field(..., description="User message describing the analysis request")


class MySQLAnalysisOutputSchema(BaseModel):
    """Output schema for MySQL analysis results."""
    company_name: str = Field(..., description="Company name analyzed")
    query_results: Dict[str, Any] = Field(..., description="Raw query results from database")
    supplier_analysis: str = Field(..., description="Analysis of supplier reliability data")
    historical_context: str = Field(..., description="Historical context for financial analysis")
    recommendations: List[str] = Field(..., description="Recommendations based on historical data")
    response: str = Field(..., description="Complete formatted response")


class FinancialResearchInputSchema(BaseIOSchema):
    """
    Specialized input schema for financial research agents.
    Extends basic instruction schema with financial-specific fields.
    """
    company_name: str = Field(
        ..., 
        description="Name of the company to research"
    )
    research_queries: list[str] = Field(
        default_factory=list,
        description="Specific search queries to execute"
    )
    focus_areas: list[str] = Field(
        default=["stock_price", "earnings", "news"],
        description="Areas to focus research on"
    )
    message: str = Field(
        ..., 
        description="Research instruction or task"
    )
    context: Optional[str] = Field(
        None, 
        description="Additional research context"
    )


class FinancialResearchOutputSchema(BaseIOSchema):
    """
    Specialized output schema for financial research results.
    Provides structured financial data with source attribution.
    """
    company_name: str = Field(
        ..., 
        description="Company name that was researched"
    )
    stock_data: Optional[dict] = Field(
        None, 
        description="Stock price and movement data"
    )
    earnings_data: Optional[dict] = Field(
        None, 
        description="Earnings information and metrics"
    )
    news_items: list[str] = Field(
        default_factory=list, 
        description="Recent news items and developments"
    )
    sources: list[str] = Field(
        default_factory=list, 
        description="Data sources and URLs"
    )
    response: str = Field(
        ..., 
        description="Comprehensive research response"
    )
    research_quality: Optional[str] = Field(
        None, 
        description="Self-assessed research quality rating"
    )


class FinancialAnalysisInputSchema(BaseIOSchema):
    """
    Input schema for financial analysis agents.
    Takes research data and analysis criteria.
    """
    company_name: str = Field(
        ..., 
        description="Company being analyzed"
    )
    research_data: str = Field(
        ..., 
        description="Raw research data to analyze"
    )
    analysis_criteria: list[str] = Field(
        default=[
            "stock_performance", 
            "earnings_analysis", 
            "strengths_concerns", 
            "recommendations"
        ],
        description="Analysis criteria to focus on"
    )
    message: str = Field(
        ..., 
        description="Analysis instruction or task"
    )
    context: Optional[str] = Field(
        None, 
        description="Additional analysis context"
    )


class FinancialAnalysisOutputSchema(BaseIOSchema):
    """
    Output schema for financial analysis results.
    Provides structured analysis with confidence metrics.
    """
    company_name: str = Field(
        ..., 
        description="Company that was analyzed"
    )
    stock_performance: Optional[str] = Field(
        None, 
        description="Stock performance analysis"
    )
    earnings_analysis: Optional[str] = Field(
        None, 
        description="Earnings performance analysis"
    )
    key_strengths: list[str] = Field(
        default_factory=list, 
        description="Identified company strengths"
    )
    key_concerns: list[str] = Field(
        default_factory=list, 
        description="Identified concerns or risks"
    )
    recommendations: Optional[str] = Field(
        None, 
        description="Analyst recommendations"
    )
    response: str = Field(
        ..., 
        description="Comprehensive analysis response"
    )
    confidence_score: Optional[float] = Field(
        None, 
        description="Analysis confidence score (0-1)",
        ge=0.0,
        le=1.0
    )


class ReportGenerationInputSchema(BaseIOSchema):
    """
    Input schema for report generation agents.
    Takes analysis data and formatting requirements.
    """
    company_name: str = Field(
        ..., 
        description="Company name for the report"
    )
    analysis_data: str = Field(
        ..., 
        description="Analysis data to include in report"
    )
    output_path: str = Field(
        ..., 
        description="File path for report output"
    )
    report_sections: list[str] = Field(
        default=[
            "header", "overview", "stock_performance", 
            "earnings", "news", "outlook", "sources"
        ],
        description="Sections to include in report"
    )
    message: str = Field(
        ..., 
        description="Report generation instruction"
    )
    context: Optional[str] = Field(
        None, 
        description="Additional report context"
    )


class ReportGenerationOutputSchema(BaseIOSchema):
    """
    Output schema for report generation results.
    Provides report metadata and success status.
    """
    company_name: str = Field(
        ..., 
        description="Company name in the report"
    )
    report_path: str = Field(
        ..., 
        description="Path to the generated report file"
    )
    report_content: str = Field(
        ..., 
        description="Generated report content"
    )
    sections_included: list[str] = Field(
        default_factory=list, 
        description="Sections that were included in the report"
    )
    word_count: Optional[int] = Field(
        None, 
        description="Report word count"
    )
    response: str = Field(
        ..., 
        description="Report generation response"
    )
    generation_success: bool = Field(
        True, 
        description="Whether report was successfully generated"
    )


class ShortageAnalysisInputSchema(BaseIOSchema):
    """
    Input schema for shortage analysis agents.
    Takes financial data and quantity information for shortage calculations.
    """
    company_name: str = Field(
        default="test_company",
        description="Name of the company being analyzed"
    )
    financial_data: Optional[str] = Field(
        default=None,
        description="Research and analysis data containing inventory/supply chain information"
    )
    required_quantities: Optional[List[float]] = Field(
        None,
        description="List of required quantities for different products/materials"
    )
    available_quantities: Optional[List[float]] = Field(
        None,
        description="List of available quantities for different products/materials"
    )
    components: Optional[Dict[str, Dict[str, int]]] = Field(
        None,
        description="Component data with available and required quantities"
    )
    weights: Optional[Dict[str, float]] = Field(
        None,
        description="Component weights for weighted shortage calculation"
    )
    message: str = Field(
        default="Analyze shortage index for the provided components",
        description="Shortage analysis instruction or task"
    )
    context: Optional[str] = Field(
        None,
        description="Additional context for shortage analysis"
    )


class ShortageAnalysisOutputSchema(BaseIOSchema):
    """
    Output schema for shortage analysis results.
    Provides structured shortage assessment with risk levels and recommendations.
    """
    company_name: str = Field(
        ..., 
        description="Company name that was analyzed"
    )
    shortage_index: float = Field(
        ..., 
        description="Calculated shortage index (0.0 to 1.0)",
        ge=0.0,
        le=1.0
    )
    risk_level: str = Field(
        ..., 
        description="Risk level classification: LOW, MEDIUM, or HIGH"
    )
    shortage_analysis: str = Field(
        ..., 
        description="Detailed shortage analysis and findings"
    )
    recommendations: List[str] = Field(
        default_factory=list, 
        description="Actionable recommendations for shortage mitigation"
    )
    response: str = Field(
        ..., 
        description="Comprehensive shortage analysis response"
    )


class AlertManagementInputSchema(BaseIOSchema):
    """
    Input schema for alert management agents.
    Takes analysis data and alert configuration for notification processing.
    """
    company_name: str = Field(
        default="test_company",
        description="Company name for alert context"
    )
    analysis_data: Optional[str] = Field(
        None,
        description="Financial and shortage analysis data for alert evaluation"
    )
    shortage_data: Optional[str] = Field(
        None,
        description="Specific shortage analysis results"
    )
    alert_message: str = Field(
        ...,
        description="Alert message content"
    )
    channels: List[str] = Field(
        default_factory=list,
        description="List of notification channels (HTTP, MQTT, EMAIL)"
    )
    severity: str = Field(
        default="MEDIUM",
        description="Alert severity level"
    )
    delivery_config: Optional[Dict[str, Any]] = Field(
        None,
        description="Delivery configuration for different channels"
    )
    alert_thresholds: Dict[str, Any] = Field(
        default_factory=dict,
        description="Threshold values for different alert types"
    )
    notification_config: Dict[str, Any] = Field(
        default_factory=dict,
        description="Notification channel configuration (email, MQTT, HTTP)"
    )
    message: str = Field(
        default="Send alert notification",
        description="Alert management instruction or task"
    )


class AlertManagementOutputSchema(BaseIOSchema):
    """
    Output schema for alert management results.
    Provides alert delivery status and notification summary.
    """
    company_name: str = Field(
        ..., 
        description="Company name for the alerts"
    )
    alerts_sent: List[str] = Field(
        default_factory=list, 
        description="List of alerts that were sent with their channels"
    )
    notification_results: Dict[str, Any] = Field(
        default_factory=dict, 
        description="Results of notification delivery attempts"
    )
    alert_summary: str = Field(
        ..., 
        description="Summary of alert evaluation and actions taken"
    )
    response: str = Field(
        ..., 
        description="Comprehensive alert management response"
    )


# Export all schemas for easy importing
__all__ = [
    "InstructionInputSchema",
    "InstructionOutputSchema", 
    "FinancialResearchInputSchema",
    "FinancialResearchOutputSchema",
    "FinancialAnalysisInputSchema", 
    "FinancialAnalysisOutputSchema",
    "ReportGenerationInputSchema",
    "ReportGenerationOutputSchema",
    "ShortageAnalysisInputSchema",
    "ShortageAnalysisOutputSchema",
    "AlertManagementInputSchema",
    "AlertManagementOutputSchema"
]
