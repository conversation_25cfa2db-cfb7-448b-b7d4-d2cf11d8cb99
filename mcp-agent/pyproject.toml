[project]
name = "mcp-agent"
version = "0.1.7"
description = "Build effective agents with Model Context Protocol (MCP) using simple, composable patterns."
readme = "README.md"
license = { file = "LICENSE" }
authors = [
    { name = "<PERSON><PERSON><PERSON>", email = "<EMAIL>" }
]
classifiers = [
    "Programming Language :: Python :: 3",
    "License :: OSI Approved :: Apache Software License",
    "Operating System :: OS Independent"
]
requires-python = ">=3.10"
dependencies = [
    "fastapi>=0.115.6",
    "instructor>=1.7.9",
    "opentelemetry-distro>=0.50b0",
    "opentelemetry-exporter-otlp-proto-http>=1.29.0",
    "pydantic-settings>=2.7.0",
    "pydantic>=2.10.4",
    "pyyaml>=6.0.2",
    "rich>=13.9.4",
    "typer>=0.15.1",
    "numpy>=2.1.3",
    "scikit-learn>=1.6.0",
    "prompt-toolkit>=3.0.50",
    "aiohttp>=3.11.13",
    "websockets>=12.0",
    "opentelemetry-instrumentation-openai>=0.39.3",
    "opentelemetry-instrumentation-anthropic>=0.39.3",
    "mcp>=1.10.1",
]

[project.optional-dependencies]
temporal = [
    "temporalio>=1.10.0",
]
anthropic = [
    "anthropic>=0.48.0",
]

anthropic_bedrock = [
    "anthropic[bedrock]>=0.52.0",
]

anthropic_vertex = [
    "anthropic[vertex]>=0.52.0",
    "google-cloud-aiplatform>=1.101.0",
]

bedrock = [
    "boto3>=1.37.23"
]
openai = [
    "openai>=1.58.1",
]
azure = [
    "azure-ai-inference>=1.0.0b9",
    "azure-identity>=1.22.0"
]
google = [
    "google-genai>=1.10.0",
]
cohere = [
    "cohere>=5.13.4",
]
langchain = [
    "langchain-core>=0.3.64",
]
crewai = [
    "crewai>=0.126.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[dependency-groups]
dev = [
    "pre-commit>=4.0.1",
    "pydantic>=2.10.4",
    "pyyaml>=6.0.2",
    "ruff>=0.8.4",
    "tomli>=2.2.1",
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.1",
    "boto3-stubs[bedrock-runtime]>=1.37.23",
    "trio>=0.30.0",
    "pytest-cov>=6.1.1",
    "httpx>=0.28.1",
]

[project.scripts]
mcp-agent = "mcp_agent.cli.__main__:app"
mcp_agent = "mcp_agent.cli.__main__:app"
mcpagent = "mcp_agent.cli.__main__:app"
silsila = "mcp_agent.cli.__main__:app"
prompt-server = "mcp_agent.mcp.prompts.__main__:main"

[tool.setuptools.packages.find]
include = ["mcp-agent"]

[tool.setuptools.package-data]
mcp-agent = [ # TODO: should this be mcp_agent?
    "data/*.json",
    "resources/examples/**/*.py",
    "resources/examples/**/*.yaml",
    "resources/examples/**/*.yml",
    "resources/examples/**/*.csv",
    "resources/examples/**/mount-point/*.csv"
]

[tool.pytest.ini_options]
pythonpath = ["."]
